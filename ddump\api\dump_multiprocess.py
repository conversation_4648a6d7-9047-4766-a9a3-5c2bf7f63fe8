#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多进程支持的Dump扩展

在不修改原有Dump类的基础上，提供多进程下载能力
"""

import pandas as pd
import multiprocessing as mp
from pathlib import Path
from loguru import logger
from concurrent.futures import ProcessPoolExecutor, as_completed


from .dump import Dump__start__end, func_pre_save


def _apply_proxy_monkey_patch(proxy_url, api_params=None):
    """应用代理猴子补丁到requests"""
    try:
        import requests

        # 保存原始函数（如果还没保存的话）
        if not hasattr(requests, '_original_request'):
            requests._original_request = requests.request   # type: ignore

        # 创建代理字典
        proxy_dict = {'http': proxy_url, 'https': proxy_url}

        # 创建代理包装函数
        def proxy_request(method, url, **kwargs):
            kwargs.setdefault('proxies', proxy_dict)
            kwargs.setdefault('timeout', api_params.get('timeout', 5) if api_params else 5)  # 默认5秒超时
            return requests._original_request(method, url, **kwargs)  # type: ignore

        # 应用猴子补丁
        requests.request = proxy_request

    except Exception as e:
        logger.warning(f'应用代理补丁失败: {e}')


def _restore_requests():
    """恢复原始requests函数"""
    try:
        import requests
        if hasattr(requests, '_original_request'):
            requests.request = requests._original_request  # type: ignore
            delattr(requests, '_original_request')
    except Exception as e:
        logger.warning(f'恢复requests失败: {e}')


def download_worker(api_module, api_params, temp_path, stock_batch,
                        func_name, download_params, kw, pre_save_func=None,
                        shared_proxy_manager=None):
    """多进程worker函数：下载一批股票数据

    Parameters
    ----------
    api_module : str
        API模块名（如'akshare'）
    api_params : dict
        API初始化参数
    temp_path : str
        临时路径
    stock_batch : list
        股票代码列表
    func_name : str
        API函数名
    download_params : dict
        下载参数（除symbol外）
    kw : list
        需要传递的参数键
    pre_save_func : callable, optional
        预处理函数
    shared_proxy_manager : SharedWeightManager, optional
        共享代理权重管理器

    Returns
    -------
    pd.DataFrame
        合并后的数据
    """
    try:
        # 在子进程中重新导入API
        if api_module == 'akshare':
            import akshare as ak
            api = ak
        else:
            raise ValueError(f"不支持的API模块: {api_module}")

        # 初始化代理设置
        current_proxy = None
        if shared_proxy_manager is not None:
            try:
                # 获取加权代理池
                proxy_pool = shared_proxy_manager.get_weighted_proxy_pool(include_direct=True)
                if proxy_pool:
                    import random
                    current_proxy = random.choice(proxy_pool)
                    logger.info(f'进程使用代理: {current_proxy}')

                    # 应用代理设置（使用猴子补丁）
                    if current_proxy != 'DIRECT':
                        _apply_proxy_monkey_patch(current_proxy, api_params)

            except Exception as e:
                logger.warning(f'代理初始化失败: {e}，使用直连')
                current_proxy = 'DIRECT'

        # 创建独立的Dump对象
        d = Dump__start__end(api, Path(temp_path), 'start_date', 'end_date')
        
        # 下载每只股票
        success_count = 0
        for symbol in stock_batch:
            # 每只股票最多5次重试机会
            stock_success = False
            for attempt in range(5):
                try:
                    # 每次重试时重新选择代理
                    if shared_proxy_manager and attempt > 0:
                        proxy_pool = shared_proxy_manager.get_weighted_proxy_pool(include_direct=True)
                        if proxy_pool:
                            import random
                            current_proxy = random.choice(proxy_pool)
                            if current_proxy != 'DIRECT':
                                _apply_proxy_monkey_patch(current_proxy, api_params)
                            else:
                                _restore_requests()

                    # 设置参数
                    params = {**download_params, 'symbol': symbol}
                    d.set_parameters(func_name, **params)

                    # 下载数据
                    d.download(kw=kw)

                    # 成功
                    stock_success = True
                    success_count += 1

                    # 更新代理权重（成功）
                    if shared_proxy_manager and current_proxy:
                        shared_proxy_manager.update_weight(current_proxy, success=True)

                    break  # 成功后跳出重试循环

                except Exception as e:
                    # 更新代理权重（失败）
                    if shared_proxy_manager and current_proxy:
                        shared_proxy_manager.update_weight(current_proxy, success=False)

                    if attempt == 4:  # 最后一次尝试
                        logger.error(f'股票 {symbol} 重试5次后仍失败: {e}')
                    else:
                        logger.warning(f'股票 {symbol} 第{attempt+1}次尝试失败: {e}，准备重试')
                        import time
                        time.sleep(1)  # 重试前等待1秒

            if not stock_success:
                logger.error(f'股票 {symbol} 最终下载失败，跳过')
        
        # 合并数据
        if d.dfs:
            merged_df = d.merge_dfs(pre_save=pre_save_func or func_pre_save)
            logger.info(f'进程完成，股票数: {len(stock_batch)}, 成功: {success_count}, 数据量: {len(merged_df)}')
            return merged_df
        else:
            logger.warning(f'进程无数据，股票: {stock_batch}')
            return pd.DataFrame()

    except Exception as e:
        logger.error(f'进程异常，股票: {stock_batch}, 错误: {e}')

        # 更新代理权重（异常）
        if shared_proxy_manager and current_proxy:
            shared_proxy_manager.update_weight(current_proxy, success=False)

        return pd.DataFrame()

    finally:
        # 恢复requests（清理猴子补丁）
        _restore_requests()


class MultiProcessDump:
    """多进程Dump类
    
    在不修改原有Dump类的基础上，提供多进程下载能力
    """
    
    def __init__(self, api_module='akshare', temp_path='./temp', max_workers=None,
                 enable_proxy=False):
        """初始化

        Parameters
        ----------
        api_module : str
            API模块名
        temp_path : str or Path
            临时路径
        max_workers : int, optional
            最大进程数，默认为CPU核心数
        enable_proxy : bool, optional
            是否启用代理支持
        """
        self.api_module = api_module
        self.temp_path = Path(temp_path)
        self.max_workers = max_workers or mp.cpu_count()
        self.results = []
        self.shared_proxy_manager = None

        # 初始化代理管理器
        if enable_proxy:
            try:
                from ..tools.proxies import create_shared_weight_manager
                self.shared_proxy_manager = create_shared_weight_manager()
                logger.info(f'初始化多进程Dump，进程数: {self.max_workers}，代理: 已启用')
            except ImportError:
                logger.warning('代理模块未找到，禁用代理支持')
                logger.info(f'初始化多进程Dump，进程数: {self.max_workers}，代理: 已禁用')
        else:
            logger.info(f'初始化多进程Dump，进程数: {self.max_workers}，代理: 已禁用')
        
    def download(self, stock_list, func_name, download_params, kw, 
                       batch_size=None, pre_save_func=None):
        """批量下载股票数据
        
        Parameters
        ----------
        stock_list : list
            股票代码列表
        func_name : str
            API函数名（如'stock_zh_a_hist'）
        download_params : dict
            下载参数（除symbol外）
        kw : list
            需要传递的参数键
        batch_size : int, optional
            每个进程处理的股票数量，默认自动计算
        pre_save_func : callable, optional
            预处理函数
            
        Returns
        -------
        list
            每个进程返回的DataFrame列表
        """
        # 计算批次大小
        if batch_size is None:
            batch_size = max(1, len(stock_list) // self.max_workers)
        
        # 分割股票列表
        stock_batches = [
            stock_list[i:i + batch_size] 
            for i in range(0, len(stock_list), batch_size)
        ]
        
        logger.info(f'开始多进程下载，股票总数: {len(stock_list)}, '
                   f'批次数: {len(stock_batches)}, 每批次: {batch_size}')
        
        # 使用进程池执行
        results = []
        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_batch = {
                executor.submit(
                    download_worker,
                    self.api_module,
                    {},  # API参数
                    str(self.temp_path),
                    batch,
                    func_name,
                    download_params,
                    kw,
                    pre_save_func,
                    self.shared_proxy_manager  # 传递代理管理器
                ): batch for batch in stock_batches
            }
            
            # 收集结果
            for future in as_completed(future_to_batch):
                batch = future_to_batch[future]
                try:
                    result = future.result()
                    if not result.empty:
                        results.append(result)
                        logger.info(f'完成批次: {len(batch)} 股票, 数据量: {len(result)}')
                    else:
                        logger.warning(f'批次无数据: {batch}')
                except Exception as e:
                    logger.error(f'批次失败: {batch}, 错误: {e}')
        
        self.results = results
        logger.info(f'多进程下载完成，有效批次: {len(results)}')
        return results
    
    def merge_results(self, pre_save_func=None):
        """合并所有进程的结果
        
        Parameters
        ----------
        pre_save_func : callable, optional
            预处理函数
            
        Returns
        -------
        pd.DataFrame
            合并后的DataFrame
        """
        if not self.results:
            logger.warning('没有结果需要合并')
            return pd.DataFrame()
        
        logger.info(f'开始合并结果，批次数: {len(self.results)}')
        
        # 应用预处理函数
        if pre_save_func:
            processed_results = [pre_save_func(df) for df in self.results]
        else:
            processed_results = self.results
        
        # 合并所有DataFrame
        merged_df = pd.concat(processed_results, ignore_index=True)
        
        logger.info(f'合并完成，最终数据量: {len(merged_df)}')
        return merged_df
    
    def download_and_merge(self, stock_list, func_name, download_params, kw,
                          batch_size=None, pre_save_func=None):
        """一体化下载和合并
        
        Parameters
        ----------
        stock_list : list
            股票代码列表
        func_name : str
            API函数名
        download_params : dict
            下载参数
        kw : list
            参数键列表
        batch_size : int, optional
            批次大小
        pre_save_func : callable, optional
            预处理函数
            
        Returns
        -------
        pd.DataFrame
            最终合并的DataFrame
        """
        # 下载
        self.download(stock_list, func_name, download_params, kw, 
                           batch_size, pre_save_func)
        
        # 合并
        return self.merge_results(pre_save_func)

