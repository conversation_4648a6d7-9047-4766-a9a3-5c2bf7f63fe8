import akshare as ak
import polars as pl
import pandas as pd
from typing import Union, Literal, Optional
from functools import lru_cache
import os
from multiprocessing import cpu_count

from ddump.tools.cache_decorators import trading_day_cache

BASE_MARKET_PREFIXES = {
    # 上海市场：主板(600,601,603,605) + 科创板(688,689)
    "SH": {'600', '601', '603', '605', '688', '689'},
    # 深圳市场：主板(000,001,002,003) + 创业板(300,301) + 新增(302)
    "SZ": {'000', '001', '002', '003', '300', '301', '302'},
    # 北京市场：新三板精选层转板等
    "BJ": {'430', '830', '831', '832', '833', '834', '835', '836', '837', '838', '839', '870', '871', '872', '873', '920'}
}


__all__ = [
    'format_stock_suffix'
]

# ============================================================================
# 性能配置函数
# ============================================================================

def configure_polars_performance(max_threads: Optional[int] = None, streaming_chunk_size: Optional[int] = None) -> None:
    """
    配置polars的性能参数，优化内存使用和并行处理
    
    Args:
        max_threads: 最大线程数，默认为CPU核心数
        streaming_chunk_size: 流式处理的块大小，默认为自动

    Performance:
        - 优化CPU并行度
        - 配置内存使用模式
        - 启用高性能设置
    """
    # 配置最大线程数
    if max_threads is None:
        max_threads = cpu_count()
    
    # 设置polars环境变量进行性能优化
    os.environ['POLARS_MAX_THREADS'] = str(max_threads)
    
    # 启用更激进的并行化
    os.environ['POLARS_FORCE_ASYNC'] = '1'
    
    # 优化字符串处理性能
    os.environ['POLARS_ACTIVATE_DECIMAL'] = '1'
    
    # 配置内存映射
    os.environ['POLARS_VERBOSE'] = '0'  # 减少日志开销
    
    # 设置streaming chunk size
    if streaming_chunk_size:
        os.environ['POLARS_STREAMING_CHUNK_SIZE'] = str(streaming_chunk_size)

# 自动配置性能参数
configure_polars_performance()

# ============================================================================
# 市场前缀获取函数
# ============================================================================

@lru_cache(maxsize=1)  # 内存缓存
@trading_day_cache(ttl_seconds=60*60*24)  # 硬盘缓存，24小时TTL
def _get_market_prefixes() -> dict:
    """
    一次性获取所有市场的股票代码前缀 - 主备数据源策略版本

    实现主备数据源策略：
    1. 主数据源：交易所官方API（优先使用）
    2. 备数据源：东方财富API（主数据源失败时使用）
    3. 兜底策略：基础前缀（两个API都失败时使用）

    Returns:
        dict: 包含所有市场前缀的字典 {"SH": set, "SZ": set, "BJ": set}

    Note:
        实现"以交易所为主、东财为备用"的策略
        使用akshare接口，结果会被缓存24小时
    """
    # 使用共用的基础前缀常量（包含最新的API数据分析结果）
    base_prefixes = BASE_MARKET_PREFIXES

    result = {}

    # 获取上海市场 - 主备策略
    result['SH'] = _get_market_prefixes_with_fallback('SH', base_prefixes['SH'])

    # 获取深圳市场 - 主备策略
    result['SZ'] = _get_market_prefixes_with_fallback('SZ', base_prefixes['SZ'])

    # 获取北京市场 - 主备策略
    result['BJ'] = _get_market_prefixes_with_fallback('BJ', base_prefixes['BJ'])

    return result


def _get_market_prefixes_with_fallback(market: str, base_prefixes: set) -> set:
    """
    使用主备策略获取指定市场的前缀

    Args:
        market: 市场代码 ('SH', 'SZ', 'BJ')
        base_prefixes: 基础前缀集合

    Returns:
        set: 该市场的前缀集合
    """

    def _check_and_warn_new_prefixes(api_prefixes: set, source_name: str):
        """检查API获取的前缀是否有新增，如有则提醒用户更新BASE_MARKET_PREFIXES"""
        new_prefixes = api_prefixes - base_prefixes
        if new_prefixes:
            print(f"⚠️ 发现{market}市场新前缀（来源：{source_name}）: {sorted(new_prefixes)}")
            print(f"💡 建议更新 BASE_MARKET_PREFIXES['{market}'] 添加: {new_prefixes}")
            print("-" * 60)
    # 策略1：尝试交易所API（主数据源）
    try:
        if market == 'SH':
            # 上海：需要合并主板和科创板
            stock_info_main = ak.stock_info_sh_name_code(symbol="主板A股")
            main_prefixes = set(stock_info_main['证券代码'].str[:3].unique())

            stock_info_kcb = ak.stock_info_sh_name_code(symbol="科创板")
            kcb_prefixes = set(stock_info_kcb['证券代码'].str[:3].unique())

            jys_prefixes = main_prefixes | kcb_prefixes
            _check_and_warn_new_prefixes(jys_prefixes, "交易所API")
            result = base_prefixes | jys_prefixes
            return result

        elif market == 'SZ':
            # 深圳：A股列表
            stock_info = ak.stock_info_sz_name_code(symbol="A股列表")
            jys_prefixes = set(stock_info['A股代码'].str[:3].unique())
            _check_and_warn_new_prefixes(jys_prefixes, "交易所API")
            result = base_prefixes | jys_prefixes
            return result

        elif market == 'BJ':
            # 北京：北交所
            stock_info = ak.stock_info_bj_name_code()
            jys_prefixes = set(stock_info['证券代码'].str[:3].unique())
            _check_and_warn_new_prefixes(jys_prefixes, "交易所API")
            result = base_prefixes | jys_prefixes
            return result

    except Exception as e:
        print(f"交易所API获取{market}前缀失败: {e}")

    # 策略2：尝试东财API（备数据源）
    try:
        if market == 'SH':
            stock_info = ak.stock_sh_a_spot_em()
        elif market == 'SZ':
            stock_info = ak.stock_sz_a_spot_em()
        elif market == 'BJ':
            stock_info = ak.stock_bj_a_spot_em()
        else:
            raise ValueError(f"不支持的市场: {market}")

        dc_prefixes = set(stock_info['代码'].str[:3].unique())
        _check_and_warn_new_prefixes(dc_prefixes, "东财API")
        result = base_prefixes | dc_prefixes
        return result

    except Exception as e:
        print(f"东财API获取{market}前缀失败: {e}")

    # 策略3：使用基础前缀（兜底策略）
    print(f"使用{market}基础前缀: {len(base_prefixes)}个")
    return base_prefixes

def _get_single_market_prefixes(market: Literal['SH', 'SZ', 'BJ']) -> set:
    """
    获取指定市场的股票代码前缀

    Args:
        market: 市场代码 ('SH', 'SZ', 'BJ')

    Returns:
        set: 该市场的所有股票代码前缀集合
    """
    all_prefixes = _get_market_prefixes()
    return all_prefixes.get(market, set())

# ============================================================================
# 预计算映射系统
# ============================================================================

@lru_cache(maxsize=1)  # 内存缓存
def _precompute_stock_mappings() -> dict:
    """
    预计算所有可能的股票代码映射表

    创建映射系统，实现O(1)复杂度的代码转换：
    - 完整代码映射：直接从完整代码到目标格式的映射
    - 前缀映射：从前缀到后缀的映射
    - 反向映射：支持所有格式之间的双向转换
    - 验证集合：快速验证代码有效性

    Returns:
        dict: 映射表，包含所有转换组合的预计算结果
    """
    # 获取所有市场的动态前缀数据
    sh_prefixes = _get_single_market_prefixes('SH')
    sz_prefixes = _get_single_market_prefixes('SZ')
    bj_prefixes = _get_single_market_prefixes('BJ')
    
    # 算法优化1：构建前缀到后缀的直接映射（用于int格式转换）
    prefix_to_suffixes = {}
    for prefix in sh_prefixes:
        prefix_to_suffixes[prefix] = {
            'wind': '.SH',
            'rq': '.XSHG',
            'jq': '.XSHG'
        }
    for prefix in sz_prefixes:
        prefix_to_suffixes[prefix] = {
            'wind': '.SZ',
            'rq': '.XSHE',
            'jq': '.XSHE'
        }
    for prefix in bj_prefixes:
        prefix_to_suffixes[prefix] = {
            'wind': '.BJ',
            'rq': '.XBEI',
            'jq': '.BJSE'
        }
    
    # 算法优化2：构建后缀替换映射（用于后缀到后缀转换）
    suffix_replacement_maps = {
        ('wind', 'rq'): {'.SH': '.XSHG', '.SZ': '.XSHE', '.BJ': '.XBEI'},
        ('wind', 'jq'): {'.SH': '.XSHG', '.SZ': '.XSHE', '.BJ': '.BJSE'},
        ('rq', 'wind'): {'.XSHG': '.SH', '.XSHE': '.SZ', '.XBEI': '.BJ'},
        ('jq', 'wind'): {'.XSHG': '.SH', '.XSHE': '.SZ', '.BJSE': '.BJ'},
        ('rq', 'jq'): {'.XSHG': '.XSHG', '.XSHE': '.XSHE', '.XBEI': '.BJSE'},
        ('jq', 'rq'): {'.XSHG': '.XSHG', '.XSHE': '.XSHE', '.BJSE': '.XBEI'}
    }
    
    # 算法优化3：构建后缀移除映射（用于转换为int格式）
    suffix_removal_patterns = {
        'wind': r'\.(SH|SZ|BJ)$',
        'rq': r'\.(XSHG|XSHE|XBEI)$',
        'jq': r'\.(XSHG|XSHE|BJSE)$'
    }
    
    all_prefixes = sh_prefixes | sz_prefixes | bj_prefixes
    
    return {
        'prefix_to_suffixes': prefix_to_suffixes,
        'suffix_replacement_maps': suffix_replacement_maps,
        'suffix_removal_patterns': suffix_removal_patterns,
        'valid_prefixes': all_prefixes,
        'sh_prefixes': sh_prefixes,
        'sz_prefixes': sz_prefixes,
        'bj_prefixes': bj_prefixes
    }


# ============================================================================
# 主要统一接口函数
# ============================================================================

def format_stock_suffix(
    df: Union[pd.DataFrame, pl.DataFrame],
    symbol_column: str,
    from_suffix: Literal['int','wind','rq','jq'],
    to_suffix: Literal['int','wind','rq','jq'],
    is_filter: bool = True,
    new_column: Optional[str] = None
) -> Union[pd.DataFrame, pl.DataFrame]:
    """
    股票代码格式转换函数 - 高性能优化版本

    集成高性能优化技术：
    - Vectorized Operations: 完全向量化操作，零Python循环
    - String Optimization: 高效字符串操作，预计算映射表
    - Parallel Processing: 最大化CPU并行度

    Args:
        df: 输入的DataFrame (支持pandas.DataFrame, polars.DataFrame)
        symbol_column: 包含股票代码的列名
        from_suffix: 输入格式 ('int', 'wind', 'rq', 'jq')
        to_suffix: 输出格式 ('int', 'wind', 'rq', 'jq')
        is_filter: 是否过滤无效代码
        new_column: 新列名，如果指定则创建新列而不是修改原列

    Returns:
        转换后的DataFrame (保持输入类型)
    """
    # 如果输入输出格式相同，直接返回
    if from_suffix == to_suffix:
        return df

    # 检测输入类型
    is_pandas = isinstance(df, pd.DataFrame)

    # 确定输出列名
    output_column = new_column if new_column is not None else symbol_column

    # DataFrame处理路径
    # 转换为polars处理
    if is_pandas:
        work_df = pl.from_pandas(df)
    else:
        work_df = df  # 已经是polars DataFrame

    # 内存优化：确保symbol列使用最优数据类型
    work_df = work_df.with_columns([
        pl.col(symbol_column).cast(pl.Utf8, strict=False)
    ])

    # 使用预计算映射表
    mappings = _precompute_stock_mappings()
    valid_prefixes = mappings['valid_prefixes']

    # 完全向量化的转换逻辑 - O(1)复杂度
    try:
        if from_suffix == 'int':
            # int格式转换：使用预计算的前缀到后缀映射
            prefix_to_suffixes = mappings['prefix_to_suffixes']

            # 提取目标后缀映射
            prefix_to_target = {prefix: suffixes[to_suffix]
                               for prefix, suffixes in prefix_to_suffixes.items()}

            # 单一表达式：补零 + 前缀提取 + 映射 + 拼接
            symbol_col = pl.col(symbol_column).cast(pl.Utf8).str.zfill(6)
            prefix_col = symbol_col.str.slice(0, 3)
            suffix_mapped = prefix_col.replace(prefix_to_target, default=None)

            # 单一向量化表达式：条件检查 + 字符串拼接
            final_expr = pl.when(suffix_mapped.is_not_null()).then(
                pl.concat_str([symbol_col, suffix_mapped], separator="")
            ).otherwise(None if is_filter else pl.col(symbol_column))

            result_df = work_df.with_columns([final_expr.alias(output_column)])

        elif to_suffix == 'int':
            # 转换为int格式：使用预计算的后缀移除模式
            removal_pattern = mappings['suffix_removal_patterns'][from_suffix]

            # 单一正则表达式移除后缀 + 前缀验证
            symbol_col = pl.col(symbol_column)
            code_extracted = symbol_col.str.replace_all(removal_pattern, '')
            prefix_valid = code_extracted.str.slice(0, 3).is_in(list(valid_prefixes))

            # 单一向量化表达式完成所有逻辑
            final_expr = pl.when(prefix_valid & (code_extracted.str.len_chars() == 6)).then(
                code_extracted
            ).otherwise(None if is_filter else symbol_col)

            result_df = work_df.with_columns([final_expr.alias(output_column)])

        else:
            # 后缀到后缀转换：使用预计算的替换映射
            replacement_map = mappings['suffix_replacement_maps'].get((from_suffix, to_suffix))

            if replacement_map:
                symbol_col = pl.col(symbol_column)

                # 使用预计算的替换映射
                if len(replacement_map) == 1:
                    # 单一映射：直接替换
                    old_suffix, new_suffix = list(replacement_map.items())[0]
                    result_expr = symbol_col.str.replace_all(old_suffix, new_suffix)
                else:
                    # 多重映射：链式替换（已经是最优的）
                    result_expr = symbol_col
                    for old_suffix, new_suffix in replacement_map.items():
                        result_expr = result_expr.str.replace_all(old_suffix, new_suffix)

                # 验证：使用预计算的前缀集合
                prefix_extracted = symbol_col.str.extract(r'^(\d{3})', 1)
                is_valid = prefix_extracted.is_in(list(valid_prefixes))

                # 根据模式选择最终表达式
                final_expr = pl.when(is_valid).then(result_expr).otherwise(
                    None if is_filter else symbol_col
                )

                result_df = work_df.with_columns([final_expr.alias(output_column)])
            else:
                # 不支持的转换组合
                raise ValueError(f"不支持的转换: {from_suffix} -> {to_suffix}")

        # 过滤：使用预计算的验证集合
        if is_filter:
            if to_suffix == 'int':
                prefix_check = pl.col(output_column).str.slice(0, 3).is_in(list(valid_prefixes))
            else:
                prefix_check = pl.col(output_column).str.extract(r'^(\d{3})', 1).is_in(list(valid_prefixes))

            result_df = result_df.filter(prefix_check & pl.col(output_column).is_not_null())
        else:
            result_df = result_df.filter(pl.col(output_column).is_not_null())

    except Exception as e:
        print(f"转换过程中发生错误: {e}")
        # 发生错误时返回原始数据
        result_df = work_df

    # 根据输入类型返回相应格式
    if is_pandas:
        # 转换为pandas
        return result_df.to_pandas()
    else:
        # 保持polars格式
        return result_df

if __name__ == "__main__":
    pass
