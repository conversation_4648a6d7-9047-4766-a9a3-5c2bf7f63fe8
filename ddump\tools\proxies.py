"""
🐾 真正的共享内存代理权重管理器

支持功能：
1. 真正的多进程共享内存 - 使用 multiprocessing.Manager()
2. 智能权重管理 - 根据成功/失败自动调整代理权重
3. 多种数据源 - 支持本地文件和Redis
4. 加权随机选择 - 根据权重智能选择代理
5. 高并发支持 - 共享内存 + 锁同步
6. 灵活配置 - 支持包含/排除直连，返回不同格式

Examples:
    # 创建共享权重管理器（在主进程中）
    manager = create_shared_weight_manager()
    
    # 在多进程函数中使用
    fetch_akshare_history_data_parallel(stock_list, shared_manager=manager)
    
    # 更新代理权重（在子进程中）
    update_shared_weight(manager, 'proxy_url', success=True)
"""

from pathlib import Path
import random
import json
import threading
import time
from multiprocessing import Manager
from typing import Dict

from ddump.common import ROOT_DATA_DIR

__all__ = [
    'create_shared_weight_manager', 
    'update_shared_weight', 
    'get_shared_weights',
    'get_proxies_with_shared_weights',
    'SharedWeightManager'
]

class SharedWeightManager:
    """真正的共享内存权重管理器"""
    
    def __init__(self):
        """在主进程中初始化共享对象"""
        self.manager = Manager()
        self.shared_weights = self.manager.dict()
        self.shared_lock = self.manager.Lock()
        
        # 加载初始权重
        self._load_initial_weights()
        
        # 启动自动保存线程
        self._start_auto_save()
        
        print("🐾 真正的共享内存权重管理器已初始化")
    
    def _load_initial_weights(self):
        """从文件加载初始权重到共享内存"""
        proxy_json = Path(ROOT_DATA_DIR) / 'proxies.json'
        
        # 默认权重
        default_weights = {'DIRECT': 5.0}
        
        if proxy_json.exists():
            try:
                with open(proxy_json, 'r', encoding='utf-8') as f:
                    file_weights = json.load(f)
                    default_weights.update(file_weights)
            except Exception as e:
                print(f"❌ 加载权重文件失败: {e}")
        
        # 从 proxies.txt 加载代理
        proxies_txt = Path(ROOT_DATA_DIR) / 'proxies.txt'
        if proxies_txt.exists():
            try:
                with open(proxies_txt, 'r', encoding='utf-8') as f:
                    for line in f:
                        proxy = line.strip()
                        if proxy and proxy != 'DIRECT' and proxy not in default_weights:
                            default_weights[proxy] = 1.0
            except Exception as e:
                print(f"❌ 读取代理文件失败: {e}")
        
        # 更新共享权重
        with self.shared_lock:
            self.shared_weights.update(default_weights)
        
        print(f"📊 加载了 {len(default_weights)} 个代理权重到共享内存")
    
    def _start_auto_save(self):
        """启动自动保存线程"""
        def auto_save():
            while True:
                time.sleep(3)  # 3秒保存间隔
                self._save_to_disk()
        
        # 创建守护线程
        save_thread = threading.Thread(target=auto_save, daemon=True)
        save_thread.start()
        print("🔄 启动自动保存线程，3秒间隔")
    
    def _save_to_disk(self):
        """保存权重到磁盘"""
        proxy_json = Path(ROOT_DATA_DIR) / 'proxies.json'
        
        try:
            with self.shared_lock:
                # 将共享字典转换为普通字典
                weights_copy = dict(self.shared_weights)
            
            if weights_copy:
                with open(proxy_json, 'w', encoding='utf-8') as f:
                    json.dump(weights_copy, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ 保存权重文件失败: {e}")
    
    def update_weight(self, proxy_url: str, success: bool = True) -> float:
        """
        更新代理权重（真正的共享内存）
        
        Args:
            proxy_url: 代理URL或'DIRECT'
            success: True=成功(+0.5), False=失败(-0.5)
        
        Returns:
            float: 更新后的权重
        """
        with self.shared_lock:
            old_weight = self.shared_weights.get(proxy_url, 1.0)
            
            if success:
                # 成功 +0.1（最高10.0）
                new_weight = min(10.0, old_weight + 0.1)
            else:
                # 失败 -0.1（最低1.0）
                new_weight = max(1.0, old_weight - 0.1)
            
            # 更新共享权重
            self.shared_weights[proxy_url] = new_weight
            return new_weight
    
    def get_weight(self, proxy_url: str) -> float:
        """获取代理权重"""
        with self.shared_lock:
            return self.shared_weights.get(proxy_url, 1.0)
    
    def get_all_weights(self) -> Dict[str, float]:
        """获取所有权重的副本"""
        with self.shared_lock:
            return dict(self.shared_weights)
    
    def get_weighted_proxy_pool(self, include_direct: bool = True) -> list:
        """获取加权代理池"""
        # 读取代理文件
        proxies_txt = Path(ROOT_DATA_DIR) / 'proxies.txt'
        if not proxies_txt.exists():
            return ['DIRECT'] if include_direct else []
        
        try:
            with open(proxies_txt, 'r', encoding='utf-8') as f:
                file_proxies = [p.strip() for p in f.readlines() if p.strip()]
        except Exception:
            file_proxies = []
        
        # 构建加权代理池
        weighted_proxies = []
        
        with self.shared_lock:
            # 添加直连选项
            if include_direct and 'DIRECT' in self.shared_weights:
                direct_weight = int(self.shared_weights['DIRECT'])
                weighted_proxies.extend(['DIRECT'] * direct_weight)
            
            # 添加代理服务器
            for proxy in file_proxies:
                if proxy == 'DIRECT':
                    continue  # 直连已经处理过了
                
                weight = int(self.shared_weights.get(proxy, 1.0))
                weighted_proxies.extend([proxy] * weight)
        
        # 打乱顺序
        random.shuffle(weighted_proxies)
        return weighted_proxies

def create_shared_weight_manager() -> SharedWeightManager:
    """
    创建共享权重管理器（必须在主进程中调用）
    
    Returns:
        SharedWeightManager: 共享权重管理器实例
    """
    return SharedWeightManager()

def update_shared_weight(manager: SharedWeightManager, proxy_url: str, success: bool = True) -> float:
    """
    更新共享权重
    
    Args:
        manager: 共享权重管理器
        proxy_url: 代理URL或'DIRECT'
        success: True=成功(+0.5), False=失败(-0.5)
    
    Returns:
        float: 更新后的权重
    """
    return manager.update_weight(proxy_url, success)

def get_shared_weights(manager: SharedWeightManager) -> Dict[str, float]:
    """
    获取共享权重字典
    
    Args:
        manager: 共享权重管理器
    
    Returns:
        Dict[str, float]: 权重字典的副本
    """
    return manager.get_all_weights()

def get_proxies_with_shared_weights(
    manager: SharedWeightManager,
    include_direct: bool = True
):
    """
    使用共享权重获取代理池列表（简化版，只支持round_robin模式）

    Args:
        manager: 共享权重管理器
        include_direct: 是否包含直连选项

    Returns:
        List[str]: 加权代理池列表
    """
    return manager.get_weighted_proxy_pool(include_direct)

if __name__ == '__main__':
    # 测试共享权重管理器
    print("🐾 测试真正的共享内存权重管理器")
    print("=" * 60)
    
    # 创建管理器
    manager = create_shared_weight_manager()
    
    # 测试权重更新
    print("\n测试权重更新:")
    new_weight = update_shared_weight(manager, 'DIRECT', True)
    print(f"DIRECT 权重更新为: {new_weight}")
    
    new_weight = update_shared_weight(manager, 'test_proxy', False)
    print(f"test_proxy 权重更新为: {new_weight}")
    
    # 获取所有权重
    weights = get_shared_weights(manager)
    print(f"\n当前权重: {list(weights.items())[:5]}...")
    
    # 测试加权代理池
    proxy_pool = get_proxies_with_shared_weights(manager, include_direct=True)
    print(f"\n加权代理池: {proxy_pool[:10]}...")  # 只显示前10个
