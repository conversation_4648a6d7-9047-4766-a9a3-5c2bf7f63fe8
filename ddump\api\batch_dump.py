#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
带进度条的批量下载扩展

在不修改原有Dump类的基础上，提供批量下载能力和进度条显示
简化版本：使用单进程 + 进度条，避免多进程复杂性
"""

import pandas as pd
from pathlib import Path
from loguru import logger
from typing import Optional, List, Dict, Any, Callable
import time

try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False
    logger.warning("tqdm未安装，进度条功能将被禁用。安装命令: pip install tqdm")

from .dump import Dump__start__end, func_pre_save


class BatchDump:
    """
    批量下载类 - 简化版
    
    提供批量下载功能，支持进度条显示，使用单进程避免复杂性
    """
    
    def __init__(self, api_module='akshare', temp_path='./temp', show_progress=True):
        """
        初始化
        
        Parameters
        ----------
        api_module : str
            API模块名（如'akshare'）
        temp_path : str or Path
            临时路径
        show_progress : bool, optional
            是否显示进度条，默认True
        """
        self.api_module = api_module
        self.temp_path = Path(temp_path)
        self.show_progress = show_progress and TQDM_AVAILABLE
        self.results = []
        
        # 导入API模块
        if api_module == 'akshare':
            import akshare as ak
            self.api = ak
        else:
            raise ValueError(f"不支持的API模块: {api_module}")
        
        logger.info(f'初始化批量下载器，API: {api_module}, 进度条: {"已启用" if self.show_progress else "已禁用"}')
    
    def download(self, stock_list: List[str], func_name: str, download_params: Dict[str, Any], 
                 kw: List[str], pre_save_func: Optional[Callable] = None, 
                 retry_times: int = 3, retry_delay: float = 1.0) -> List[pd.DataFrame]:
        """
        批量下载股票数据
        
        Parameters
        ----------
        stock_list : list
            股票代码列表
        func_name : str
            API函数名（如'stock_zh_a_hist'）
        download_params : dict
            下载参数（除symbol外）
        kw : list
            需要传递的参数键
        pre_save_func : callable, optional
            预处理函数
        retry_times : int, optional
            重试次数，默认3次
        retry_delay : float, optional
            重试延迟（秒），默认1秒
            
        Returns
        -------
        list
            DataFrame列表
        """
        logger.info(f'开始批量下载，股票总数: {len(stock_list)}')
        
        # 初始化进度条
        progress_bar = None
        if self.show_progress:
            progress_bar = tqdm(
                total=len(stock_list),
                desc="下载股票数据",
                unit="股票",
                ncols=100,
                bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]'
            )
        
        # 创建Dump对象
        d = Dump__start__end(self.api, self.temp_path, 'start_date', 'end_date')
        
        # 统计信息
        success_count = 0
        failed_stocks = []
        
        # 逐个下载股票
        for i, symbol in enumerate(stock_list):
            stock_success = False
            
            # 重试机制
            for attempt in range(retry_times):
                try:
                    # 设置参数
                    params = {**download_params, 'symbol': symbol}
                    d.set_parameters(func_name, **params)
                    
                    # 下载数据
                    d.download(kw=kw)
                    
                    # 成功
                    stock_success = True
                    success_count += 1
                    
                    # 更新进度条
                    if progress_bar:
                        progress_bar.update(1)
                        progress_bar.set_postfix({
                            "成功": success_count,
                            "失败": len(failed_stocks),
                            "当前": symbol
                        })
                    else:
                        # 无进度条时显示详细日志
                        logger.info(f'完成 {symbol} ({i+1}/{len(stock_list)})')
                    
                    break  # 成功后跳出重试循环
                    
                except Exception as e:
                    if attempt == retry_times - 1:  # 最后一次尝试
                        logger.error(f'股票 {symbol} 重试{retry_times}次后仍失败: {e}')
                        failed_stocks.append(symbol)
                        # 失败也要更新进度条
                        if progress_bar:
                            progress_bar.update(1)
                            progress_bar.set_postfix({
                                "成功": success_count,
                                "失败": len(failed_stocks),
                                "当前": f"{symbol}(失败)"
                            })
                    else:
                        logger.warning(f'股票 {symbol} 第{attempt+1}次尝试失败: {e}，准备重试')
                        time.sleep(retry_delay)  # 重试前等待
        
        # 关闭进度条
        if progress_bar:
            progress_bar.close()
        
        # 合并数据
        if d.dfs:
            merged_df = d.merge_dfs(pre_save=pre_save_func or func_pre_save)
            self.results = [merged_df]
            logger.info(f'批量下载完成！成功: {success_count}, 失败: {len(failed_stocks)}, 数据量: {len(merged_df)}')
        else:
            self.results = []
            logger.warning(f'批量下载完成，但无有效数据。成功: {success_count}, 失败: {len(failed_stocks)}')
        
        # 显示失败的股票
        if failed_stocks:
            logger.warning(f'失败的股票: {failed_stocks}')
        
        return self.results
    
    def get_merged_data(self, pre_save_func: Optional[Callable] = None) -> pd.DataFrame:
        """
        获取合并后的数据
        
        Parameters
        ----------
        pre_save_func : callable, optional
            预处理函数
            
        Returns
        -------
        pd.DataFrame
            合并后的DataFrame
        """
        if not self.results:
            logger.warning('没有数据需要合并')
            return pd.DataFrame()
        
        if len(self.results) == 1:
            return self.results[0]
        
        # 应用预处理函数
        if pre_save_func:
            processed_results = [pre_save_func(df) for df in self.results]
        else:
            processed_results = self.results
        
        # 合并所有DataFrame
        merged_df = pd.concat(processed_results, ignore_index=True)
        logger.info(f'数据合并完成，最终数据量: {len(merged_df)}')
        return merged_df


# 为了保持向后兼容，提供一个简化的MultiProcessDump别名
class MultiProcessDump(BatchDump):
    """
    向后兼容的多进程Dump类
    
    实际上使用单进程实现，但保持相同的接口
    """
    
    def __init__(self, api_module='akshare', temp_path='./temp', max_workers=None,
                 enable_proxy=False, show_progress=True):
        """
        初始化（兼容原接口）
        
        Parameters
        ----------
        api_module : str
            API模块名
        temp_path : str or Path
            临时路径
        max_workers : int, optional
            最大进程数（已忽略，保持兼容性）
        enable_proxy : bool, optional
            是否启用代理支持（已忽略，保持兼容性）
        show_progress : bool, optional
            是否显示进度条，默认True
        """
        # 忽略多进程和代理相关参数，简化实现
        if max_workers is not None:
            logger.info(f'注意：max_workers参数已忽略，使用单进程实现')
        if enable_proxy:
            logger.info(f'注意：enable_proxy参数已忽略，代理功能已移除')
        
        super().__init__(api_module=api_module, temp_path=temp_path, show_progress=show_progress)
    
    def merge_results(self, pre_save_func: Optional[Callable] = None) -> pd.DataFrame:
        """
        合并所有结果（兼容原接口）
        
        Parameters
        ----------
        pre_save_func : callable, optional
            预处理函数
            
        Returns
        -------
        pd.DataFrame
            合并后的DataFrame
        """
        return self.get_merged_data(pre_save_func)
