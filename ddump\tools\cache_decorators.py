"""
交易日缓存装饰器

基于交易日的智能缓存策略，支持TTL自动清理。

使用示例：
    @trading_day_cache()
    def fetch_data():
        return api_call()

    @trading_day_cache(ttl_seconds=86400)  # 1天后清理
    def fetch_with_ttl():
        return api_call()
"""

import functools
import time
from pathlib import Path
from typing import Callable, Optional
import joblib

from ddump.common import START_SEP_END, QUANT_CACHE_PATH
from ddump.tools.date import get_trade_date_now

__all__ = [
    'trading_day_cache'
]


def _generate_cache_key(func, args, kwargs, current_date):
    """生成缓存key"""
    func_name = func.__name__

    if args or kwargs:
        # 有参数函数：使用参数hash
        try:
            param_hash = joblib.hash((args, kwargs))
            if param_hash and len(param_hash) >= 8:
                return f"{func_name}_{param_hash[:8]}{START_SEP_END}{current_date}"
            else:
                return f"{func_name}_with_params{START_SEP_END}{current_date}"
        except Exception:
            return f"{func_name}_with_params{START_SEP_END}{current_date}"
    else:
        # 无参数函数
        return f"{func_name}_no_params{START_SEP_END}{current_date}"


def _get_cache_path(func, cache_dir, cache_key):
    """获取缓存文件路径"""
    if cache_dir is None:
        # 使用默认缓存根目录
        cache_root_path = Path(QUANT_CACHE_PATH)
    else:
        cache_root_path = Path(cache_dir)

    # 在缓存根目录下创建以函数名命名的子文件夹
    func_name = func.__name__
    cache_dir_path = cache_root_path / func_name

    cache_dir_path.mkdir(parents=True, exist_ok=True)
    return cache_dir_path / f"{cache_key}.pkl"


def trading_day_cache(cache_dir: Optional[str] = None, ttl_seconds: Optional[int] = None):
    """
    交易日缓存装饰器

    基于交易日的缓存策略，支持TTL自动清理和手动清空缓存。

    Args:
        cache_dir: 缓存目录，None时使用函数所在文件的同目录
        ttl_seconds: TTL秒数，None时不清理。常用值：86400=1天，604800=7天

    被装饰函数的额外参数:
        clean (bool): 是否忽略当前缓存，重新拉取数据。默认False

    使用示例:
        @trading_day_cache(ttl_seconds=86400)
        def fetch_data():
            return expensive_operation()

        # 正常使用缓存
        data = fetch_data()

        # 忽略缓存，重新获取
        data = fetch_data(clean=True)

    Raises:
        ValueError: 当 ttl_seconds 不是正整数时抛出
    """
    # 参数验证
    if ttl_seconds is not None:
        if not isinstance(ttl_seconds, int) or ttl_seconds <= 0:
            raise ValueError(f"ttl_seconds 必须是正整数，当前值：{ttl_seconds}")

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # 检查是否有 clean 参数
                clean = kwargs.pop('clean', False)

                current_date = get_trade_date_now(output_date_type='str')
                cache_key = _generate_cache_key(func, args, kwargs, current_date)
                cache_path = _get_cache_path(func, cache_dir, cache_key)

                # 如果 clean=True，删除现有缓存
                if clean and cache_path.exists():
                    cache_path.unlink()
                    print(f"🗑️ 清空缓存：{cache_path.name}")

                # 检查缓存
                if cache_path.exists():
                    try:
                        # 如果设置了TTL，检查文件是否过期
                        if ttl_seconds is not None:
                            file_age = time.time() - cache_path.stat().st_mtime
                            if file_age > ttl_seconds:
                                # 文件过期，删除并重新执行函数
                                cache_path.unlink()
                                print(f"🗑️ 缓存已过期，删除：{cache_path.name}")
                            else:
                                cached_data = joblib.load(cache_path)
                                print(f"✅ 读取缓存：{cache_path.name}")
                                return cached_data
                        else:
                            cached_data = joblib.load(cache_path)
                            print(f"✅ 读取缓存：{cache_path.name}")
                            return cached_data
                    except Exception:
                        pass

                # 执行函数并缓存
                result = func(*args, **kwargs)

                try:
                    joblib.dump(result, cache_path, compress=3)
                    print(f"💾 数据已缓存到：{cache_path.name}")
                    if ttl_seconds is not None:
                        _cleanup_if_needed(cache_path.parent, ttl_seconds)
                except Exception:
                    pass

                return result
                
            except Exception as e:
                # 如果缓存过程出现任何错误，直接执行原函数
                print(f"警告：缓存机制失败，直接执行原函数。错误：{e}")
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


def _cleanup_if_needed(cache_dir: Path, ttl_seconds: int):
    marker_file = cache_dir / '.ttl_cleanup_last'
    current_time = time.time()

    if marker_file.exists():
        try:
            last_cleanup = float(marker_file.read_text())
            if current_time - last_cleanup < 3600:  # 1小时内已清理
                return
        except:
            pass

    # 执行清理
    try:
        deleted_count = 0
        total_size = 0

        for pkl_file in cache_dir.glob('*.pkl'):
            try:
                if current_time - pkl_file.stat().st_mtime > ttl_seconds:
                    total_size += pkl_file.stat().st_size
                    pkl_file.unlink()
                    deleted_count += 1
            except:
                pass

        if deleted_count > 0:
            size_mb = total_size / (1024 * 1024)
            print(f"🧹 TTL清理：删除 {deleted_count} 个过期文件，释放 {size_mb:.2f} MB")
        marker_file.write_text(str(current_time))
    except:
        pass