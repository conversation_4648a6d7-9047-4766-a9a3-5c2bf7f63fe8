#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试ddump批量下载框架（简化版）

使用ddump的BatchDump类，结合akshare的stock_zh_a_hist接口，
实现批量下载A股历史数据的功能测试。

主要功能：
1. 获取A股股票池
2. 使用单进程批量下载股票历史数据
3. 🆕 集成tqdm进度条，实时显示下载进度

进度条功能：
- 自动检测tqdm是否安装
- 显示实时下载进度（已完成/总数）
- 显示下载速度和预计剩余时间
- 支持优雅的进度条样式
- 可通过show_progress=False禁用

安装tqdm（可选）：
pip install tqdm
"""

import pandas as pd
import akshare as ak
from pathlib import Path
from loguru import logger
import time

from ddump.api.batch_dump import BatchDump, MultiProcessDump


def get_stocks_pool(return_type='list', limit=None):
    """
    获取A股股票池

    Parameters
    ----------
    return_type : str
        返回类型，'list'返回股票代码列表
    limit : int, optional
        限制返回的股票数量，用于测试

    Returns
    -------
    list
        股票代码列表（不带后缀）
    """
    try:
        # 使用akshare获取A股股票基本信息
        logger.info("正在获取A股股票池...")

        # 获取沪深A股股票信息
        stock_info_sh = ak.stock_info_sh_name_code(symbol="主板A股")
        stock_info_sz = ak.stock_info_sz_name_code(symbol="A股列表")

        # 合并股票代码
        sh_codes = stock_info_sh['A股代码'].tolist() if 'A股代码' in stock_info_sh.columns else []
        sz_codes = stock_info_sz['A股代码'].tolist() if 'A股代码' in stock_info_sz.columns else []

        # 合并并去重
        all_codes = list(set(sh_codes + sz_codes))

        # 过滤掉无效代码
        valid_codes = [code for code in all_codes if code and len(str(code)) == 6]

        # 限制数量（用于测试）
        if limit:
            valid_codes = valid_codes[:limit]

        logger.info(f"获取到 {len(valid_codes)} 只股票")

        if return_type == 'list':
            return valid_codes
        else:
            return pd.DataFrame({'code': valid_codes})

    except Exception as e:
        logger.error(f"获取股票池失败: {e}")
        # 返回一些测试用的股票代码
        test_codes = ['000001', '000002', '600000', '600036', '000858']
        if limit:
            test_codes = test_codes[:limit]
        logger.warning(f"使用测试股票池: {test_codes}")
        return test_codes


def test_batch_download():
    """
    测试批量下载股票历史数据（简化版）
    """
    logger.info("🐾 开始测试ddump批量下载框架（简化版）")

    # 配置参数
    start_date = "20250101"
    end_date = "20250715"
    temp_path = Path("./temp_test")
    temp_path.mkdir(exist_ok=True)

    # 获取股票池（测试用少量股票）
    stock_pool = get_stocks_pool(return_type='list', limit=8)
    # 确保是字符串列表
    if isinstance(stock_pool, list):
        stock_list = [str(code) for code in stock_pool]
    else:
        stock_list = ['000001', '000002', '600000']  # 默认测试股票
    logger.info(f"测试股票池: {stock_list}")

    # 初始化批量下载器（启用进度条）
    batch_dump = BatchDump(
        api_module='akshare',
        temp_path=str(temp_path),
        show_progress=True  # 🎯 启用进度条
    )

    # 设置下载参数
    download_params = {
        'period': 'daily',
        'start_date': start_date,
        'end_date': end_date,
        'adjust': ''  # 不复权
    }

    # 需要传递的参数键
    kw = ['symbol', 'period', 'start_date', 'end_date', 'adjust']

    # 开始批量下载
    start_time = time.time()

    try:
        results = batch_dump.download(
            stock_list=stock_list,
            func_name='stock_zh_a_hist',
            download_params=download_params,
            kw=kw,
            retry_times=2,  # 减少重试次数
            retry_delay=0.5  # 减少重试延迟
        )

        end_time = time.time()

        # 统计结果
        total_records = sum(len(df) for df in results if not df.empty)
        logger.info(f"✅ 下载完成！")
        logger.info(f"📊 统计信息:")
        logger.info(f"   - 处理股票数: {len(stock_list)}")
        logger.info(f"   - 有效结果数: {len(results)}")
        logger.info(f"   - 总记录数: {total_records}")
        logger.info(f"   - 耗时: {end_time - start_time:.2f}秒")
        logger.info(f"   - 平均速度: {len(stock_list)/(end_time - start_time):.2f} 股票/秒")

        # 展示部分数据
        if results and not results[0].empty:
            logger.info(f"📋 数据样例:")
            print(results[0].head())

        return True

    except Exception as e:
        logger.error(f"❌ 批量下载失败: {e}")
        return False

    finally:
        # 清理临时文件
        import shutil
        if temp_path.exists():
            shutil.rmtree(temp_path)
            logger.info("🧹 清理临时文件完成")


def test_progress_bar_only():
    """
    测试进度条功能（不实际下载数据）
    """
    logger.info("🎯 测试进度条功能")

    try:
        from tqdm import tqdm
        import time

        # 模拟批量下载进度
        stock_count = 15
        with tqdm(total=stock_count, desc="模拟下载", unit="股票", ncols=100) as pbar:
            for i in range(stock_count):
                time.sleep(0.1)  # 模拟下载时间
                pbar.update(1)
                pbar.set_postfix({"当前": f"股票{i+1:03d}"})

        logger.info("✅ 进度条测试完成")
        return True

    except ImportError:
        logger.warning("⚠️ tqdm未安装，无法测试进度条功能")
        return False
    except Exception as e:
        logger.error(f"❌ 进度条测试失败: {e}")
        return False


def test_compatibility():
    """
    测试向后兼容性（MultiProcessDump别名）
    """
    logger.info("🔄 测试向后兼容性")

    try:
        # 使用原来的MultiProcessDump接口
        multi_dump = MultiProcessDump(
            api_module='akshare',
            temp_path='./temp_compat',
            max_workers=4,  # 这个参数会被忽略
            enable_proxy=True,  # 这个参数会被忽略
            show_progress=True
        )

        # 简单验证对象创建成功
        assert multi_dump is not None
        assert hasattr(multi_dump, 'download')

        logger.info("✅ 向后兼容性测试通过")
        return True

    except Exception as e:
        logger.error(f"❌ 向后兼容性测试失败: {e}")
        return False


if __name__ == '__main__':
    logger.info("=" * 60)
    logger.info("🚀 ddump批量下载框架测试（简化版）")
    logger.info("=" * 60)

    # 测试0: 进度条功能
    logger.info("\n📍 测试0: 进度条功能")
    test_progress_bar_only()

    # 测试1: 向后兼容性
    logger.info("\n📍 测试1: 向后兼容性")
    test_compatibility()

    # 测试2: 批量下载
    logger.info("\n📍 测试2: 批量下载（带进度条）")
    success = test_batch_download()

    if success:
        logger.info("\n🎉 所有测试完成！批量下载框架运行正常")
        logger.info("\n💡 优势:")
        logger.info("   ✅ 代码简洁，易于理解和维护")
        logger.info("   ✅ 进度条实时显示下载状态")
        logger.info("   ✅ 自动重试机制，提高成功率")
        logger.info("   ✅ 向后兼容，无需修改现有代码")
        logger.info("   ✅ 单进程实现，避免多进程复杂性")
    else:
        logger.error("\n💥 测试失败，请检查配置和网络连接")