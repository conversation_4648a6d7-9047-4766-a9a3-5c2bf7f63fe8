"""
中国A股交易日历生成和多库验证工具
使用3个专业金融库获取中国A股交易日历：akshare, baostock, pandas-market-calendars
"""

from datetime import datetime
from typing import List, Optional
from pathlib import Path

import pandas as pd
import baostock as bs
import pandas_market_calendars as mcal
import akshare as ak

from ddump.common import ROOT_DATA_DIR

def get_trading_days_akshare(start_date: str, end_date: str) -> List[int]:
    """使用akshare获取交易日历"""
    try:
        df = ak.tool_trade_date_hist_sina()
        df['trade_date'] = pd.to_datetime(df['trade_date'])

        # 转换输入格式 '20090101' -> '2009-01-01'
        start_dt = pd.to_datetime(start_date[:4] + '-' + start_date[4:6] + '-' + start_date[6:8])
        end_dt = pd.to_datetime(end_date[:4] + '-' + end_date[4:6] + '-' + end_date[6:8])

        filtered_df = df[
            (df['trade_date'] >= start_dt) &
            (df['trade_date'] <= end_dt)
        ]

        trading_days = [int(day.strftime('%Y%m%d')) for day in filtered_df['trade_date']]
        trading_days.sort()
        return trading_days
    except Exception:
        return []


def get_trading_days_baostock(start_date: str, end_date: str) -> List[int]:
    """
    使用baostock获取中国A股交易日历

    Parameters:
    -----------
    start_date: str
        开始日期，格式 "YYYYMMDD"
    end_date: str
        结束日期，格式 "YYYYMMDD"

    Returns:
    --------
    List[int]: 交易日列表，格式 [20200101, 20200102, ...]
    """
    try:
        # 转换日期格式为baostock需要的格式
        start_formatted = f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:8]}"
        end_formatted = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]}"

        # 登录baostock
        lg = bs.login()
        if lg.error_code != '0':
            return []

        try:
            # 查询交易日历
            rs = bs.query_trade_dates(start_date=start_formatted, end_date=end_formatted)

            if rs.error_code != '0':
                return []

            # 提取交易日
            trading_days = []
            while (rs.error_code == '0') & rs.next():
                row = rs.get_row_data()
                # row格式: [calendar_date, is_trading_day]
                if len(row) >= 2 and row[1] == '1':  # is_trading_day == '1'
                    date_str = row[0].replace('-', '')  # 转换为YYYYMMDD格式
                    trading_days.append(int(date_str))

            return sorted(trading_days)

        finally:
            # 确保登出
            bs.logout()

    except Exception:
        return []


def get_trading_days_pandas_market_calendars(start_date: str, end_date: str) -> List[int]:
    """使用pandas-market-calendars获取交易日历"""
    try:
        xshg = mcal.get_calendar('XSHG')

        # 转换输入格式 '20090101' -> '2009-01-01'
        start_str = start_date[:4] + '-' + start_date[4:6] + '-' + start_date[6:8]
        end_str = end_date[:4] + '-' + end_date[4:6] + '-' + end_date[6:8]

        schedule = xshg.schedule(start_date=start_str, end_date=end_str)
        trading_days = [int(day.strftime('%Y%m%d')) for day in schedule.index]
        trading_days.sort()
        return trading_days
    except Exception:
        return []


def validate_calendars(start_date: str, end_date: str) -> Optional[List[int]]:
    """验证3个库的交易日历是否完全一致，返回验证后的数据"""
    akshare_data = get_trading_days_akshare(start_date, end_date)
    baostock_data = get_trading_days_baostock(start_date, end_date)
    pandas_data = get_trading_days_pandas_market_calendars(start_date, end_date)

    # 检查是否所有库都有数据
    if not akshare_data or not baostock_data or not pandas_data:
        print("❌ 部分库获取数据失败")
        return None

    # 检查数据是否完全一致
    if akshare_data == baostock_data == pandas_data:
        return akshare_data
    else:
        print("❌ 3个库的数据不一致，不允许保存")
        return None


def save_calendar_to_file(output_path: Optional[str] = None, start_date: str = "20060101",
                         end_date: Optional[str] = None) -> bool:
    """保存交易日历到文件

    Parameters:
    -----------
    output_path: Optional[str]
        输出文件路径。如果为None，默认保存到ROOT_DATA_DIR/calendars.txt，
        如果ROOT_DATA_DIR不存在则保存到当前文件所在目录/calendars.txt
    start_date: str
        开始日期，格式"YYYYMMDD"，不要早于20060101，有个包缺数据
    end_date: Optional[str]
        结束日期，格式"YYYYMMDD"，如果为None则默认到当年年底
    """
    if end_date is None:
        current_year = datetime.now().year
        end_date = f"{current_year}1231"

    # 生成输出路径
    if output_path is None:
        try:
            # 优先使用ROOT_DATA_DIR作为基础目录
            root_data_path = Path(ROOT_DATA_DIR)
            # 尝试创建目录
            root_data_path.mkdir(parents=True, exist_ok=True)
            output_file = root_data_path / "calendars.txt"
        except Exception:
            # 如果创建目录失败或有任何异常，回退到当前文件所在目录
            current_dir = Path(__file__).parent
            output_file = current_dir / "calendars.txt"
    else:
        output_file = Path(output_path)

    # 验证数据一致性并获取数据
    trading_days = validate_calendars(start_date, end_date)
    if trading_days is None:
        return False

    try:
        # 确保输出目录存在（对于手动指定路径的情况）
        output_file.parent.mkdir(parents=True, exist_ok=True)

        with open(output_file, 'w', encoding='utf-8') as f:
            for trading_day in trading_days:
                f.write(f"{trading_day}\n")

        print(f"✅ 交易日历已保存到: {output_file}")
        return True

    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return False


if __name__ == "__main__":
    # 设置参数
    start_date = "20000101"
    end_date = None  # 默认到当年年底

    # 计算实际的结束日期用于显示
    if end_date is None:
        current_year = datetime.now().year
        actual_end_date = f"{current_year}1231"
        display_end_date = f"{current_year}-12-31"
    else:
        actual_end_date = end_date
        display_end_date = end_date[:4] + '-' + end_date[4:6] + '-' + end_date[6:8]

    print("中国A股交易日历生成和多库验证工具")
    print("=" * 50)
    print(f"开始日期: {start_date[:4]}-{start_date[4:6]}-{start_date[6:8]}")
    print(f"结束日期: {display_end_date}")
    print("=" * 50)

    # 执行交易日历生成和验证（使用默认路径：ROOT_DATA_DIR/calendars.txt）
    success = save_calendar_to_file(None, start_date, actual_end_date)

    if success:
        print("\n🎉 任务完成!")
    else:
        print("\n💥 任务失败!")