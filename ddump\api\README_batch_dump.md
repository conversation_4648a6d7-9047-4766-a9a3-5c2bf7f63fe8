# ddump批量下载框架 - 简化版

## 概述

`BatchDump` 是ddump框架的简化版批量下载工具，专注于提供简洁易用的批量下载功能，同时集成了美观的进度条显示。

### 主要特性

- ✅ **简洁易用**: 单进程实现，避免多进程复杂性
- ✅ **进度条显示**: 集成tqdm，实时显示下载进度
- ✅ **自动重试**: 内置重试机制，提高下载成功率
- ✅ **向后兼容**: 保持与原MultiProcessDump接口兼容
- ✅ **错误处理**: 完善的异常处理和日志记录

## 快速开始

### 安装依赖

```bash
# 可选：安装tqdm以启用进度条功能
pip install tqdm
```

### 基本使用

```python
from ddump.api.batch_dump import BatchDump

# 创建批量下载器
batch_dump = BatchDump(
    api_module='akshare',
    temp_path='./temp',
    show_progress=True  # 启用进度条
)

# 设置下载参数
stock_list = ['000001', '000002', '600000']
download_params = {
    'period': 'daily',
    'start_date': '20250101',
    'end_date': '20250715',
    'adjust': ''
}
kw = ['symbol', 'period', 'start_date', 'end_date', 'adjust']

# 开始下载
results = batch_dump.download(
    stock_list=stock_list,
    func_name='stock_zh_a_hist',
    download_params=download_params,
    kw=kw
)

# 获取合并数据
merged_data = batch_dump.get_merged_data()
```

### 向后兼容使用

```python
from ddump.api.batch_dump import MultiProcessDump

# 使用原来的接口（实际上是BatchDump的别名）
multi_dump = MultiProcessDump(
    api_module='akshare',
    temp_path='./temp',
    max_workers=4,      # 会被忽略
    enable_proxy=True,  # 会被忽略
    show_progress=True
)

# 其他使用方式完全相同
```

## API参考

### BatchDump类

#### 构造函数

```python
BatchDump(api_module='akshare', temp_path='./temp', show_progress=True)
```

**参数:**
- `api_module` (str): API模块名，默认'akshare'
- `temp_path` (str|Path): 临时文件路径，默认'./temp'
- `show_progress` (bool): 是否显示进度条，默认True

#### download方法

```python
download(stock_list, func_name, download_params, kw, 
         pre_save_func=None, retry_times=3, retry_delay=1.0)
```

**参数:**
- `stock_list` (List[str]): 股票代码列表
- `func_name` (str): API函数名
- `download_params` (Dict): 下载参数（除symbol外）
- `kw` (List[str]): 需要传递的参数键
- `pre_save_func` (Callable, optional): 预处理函数
- `retry_times` (int): 重试次数，默认3
- `retry_delay` (float): 重试延迟（秒），默认1.0

**返回:**
- `List[pd.DataFrame]`: DataFrame列表

#### get_merged_data方法

```python
get_merged_data(pre_save_func=None)
```

**参数:**
- `pre_save_func` (Callable, optional): 预处理函数

**返回:**
- `pd.DataFrame`: 合并后的DataFrame

## 进度条功能

### 启用进度条

```python
batch_dump = BatchDump(show_progress=True)
```

进度条会显示：
- 当前进度 (已完成/总数)
- 下载速度 (股票/秒)
- 预计剩余时间
- 成功/失败统计
- 当前处理的股票代码

### 禁用进度条

```python
batch_dump = BatchDump(show_progress=False)
```

禁用后会使用传统的日志输出方式。

### 自动检测

如果未安装tqdm，进度条功能会自动禁用，并显示警告信息。

## 错误处理

### 重试机制

```python
results = batch_dump.download(
    stock_list=stock_list,
    func_name='stock_zh_a_hist',
    download_params=download_params,
    kw=kw,
    retry_times=5,      # 每只股票最多重试5次
    retry_delay=2.0     # 重试间隔2秒
)
```

### 失败处理

- 单只股票下载失败不会影响其他股票
- 失败的股票会记录在日志中
- 进度条会正确更新失败的股票数量

## 与原版本对比

| 特性 | 原MultiProcessDump | 新BatchDump |
|------|-------------------|-------------|
| 实现方式 | 多进程 | 单进程 |
| 代码复杂度 | 高 | 低 |
| 进度条 | 复杂实现 | 简洁美观 |
| 错误处理 | 复杂 | 简单明了 |
| 维护成本 | 高 | 低 |
| 性能 | 高并发 | 适中 |
| 稳定性 | 一般 | 高 |

## 最佳实践

1. **合理设置重试参数**
   ```python
   # 网络不稳定时增加重试次数和延迟
   results = batch_dump.download(
       retry_times=5,
       retry_delay=2.0
   )
   ```

2. **使用进度条监控**
   ```python
   # 启用进度条以监控下载状态
   batch_dump = BatchDump(show_progress=True)
   ```

3. **合理分批处理**
   ```python
   # 对于大量股票，可以分批处理
   for i in range(0, len(all_stocks), 100):
       batch = all_stocks[i:i+100]
       results = batch_dump.download(stock_list=batch, ...)
   ```

## 故障排除

### 常见问题

1. **进度条不显示**
   - 检查是否安装了tqdm: `pip install tqdm`
   - 确认show_progress=True

2. **下载失败率高**
   - 增加重试次数和延迟
   - 检查网络连接
   - 验证API参数是否正确

3. **内存占用过高**
   - 减少批次大小
   - 及时清理临时文件

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
batch_dump = BatchDump(show_progress=False)  # 禁用进度条以查看详细日志
```

## 更新日志

### v1.0.0 (简化版)
- 移除多进程复杂性
- 集成tqdm进度条
- 简化API接口
- 改进错误处理
- 保持向后兼容
