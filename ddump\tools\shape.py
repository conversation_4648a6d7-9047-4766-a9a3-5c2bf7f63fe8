import polars as pl
import pandas as pd
from functools import reduce
from typing import List, Optional, Union, Literal
from pathlib import Path
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
import multiprocessing as mp
import os
from ddump.common import START_SEP_END
from ddump.tools.date import parse_date_input_df

__all__ = [
    'split_df_by_days',
    'merge_dfs'
]


def _configure_polars_performance(n_jobs: int) -> None:
    """
    统一配置polars性能参数

    Args:
        n_jobs: 并行线程数，用于设置POLARS_MAX_THREADS环境变量
    """
    pl.Config.set_streaming_chunk_size(100000)  # 增大流式处理块大小
    os.environ['POLARS_MAX_THREADS'] = str(n_jobs)


def _write_group_parallel(group_data, output_path: Path, file_prefix: str, start_sep_end: str) -> int:
    """
    并行写入单个分组数据

    Args:
        group_data: polars group_by返回的(date_key, group_df)元组
        output_path: 输出目录路径
        file_prefix: 文件名前缀
        start_sep_end: 分隔符

    Returns:
        int: 写入的行数，失败时返回0
    """
    try:
        date_key, group_df = group_data

        # 处理polars group_by返回的tuple key
        date_value = date_key[0]

        date_str = date_value.strftime('%Y%m%d')
        filename = f"{file_prefix}{start_sep_end}{date_str}.parquet"
        filepath = output_path / filename

        # 内存优化：只在需要时移除临时列，避免不必要的DataFrame副本
        if '__date_key' in group_df.columns:
            # 使用select排除临时列，比drop更高效
            columns_to_keep = [col for col in group_df.columns if col != '__date_key']
            group_df = group_df.select(columns_to_keep)

        # 优化写入参数，提升I/O性能
        group_df.write_parquet(
            filepath,
            compression='lz4',
            use_pyarrow=True,
            row_group_size=50000,
            statistics=False
        )

        return len(group_df)

    except Exception as e:
        print(f"❌ 写入文件失败: {e}")
        return 0


def _process_date_range_batch(df_lazy: pl.LazyFrame, date_range: list, output_path: Path,
                                       file_prefix: str, start_sep_end: str, date_key_col: str = '__date_key') -> int:
    """
    批量处理指定日期范围的数据

    Args:
        df_lazy: 输入的LazyFrame
        date_range: 要处理的日期列表
        output_path: 输出目录路径
        file_prefix: 文件名前缀
        start_sep_end: 分隔符
        date_key_col: 日期键列名，默认为 '__date_key'

    Returns:
        int: 处理的总行数
    """
    total_rows = 0
    try:
        if not date_range:
            return 0

        # 一次性filter整个日期范围
        min_date = min(date_range)
        max_date = max(date_range)

        range_data = (
            df_lazy
            .filter(
                (pl.col(date_key_col) >= min_date) &
                (pl.col(date_key_col) <= max_date)
            )
            .collect()
        )

        if len(range_data) == 0:
            return 0

        # 使用polars原生group_by，充分利用多核
        grouped_data = list(range_data.group_by(date_key_col, maintain_order=False))

        # 筛选有效分组
        valid_groups = []
        for group_data in grouped_data:
            date_key = group_data[0]
            actual_date = date_key[0]

            if actual_date in date_range:
                valid_groups.append(group_data)

        # 并行I/O写入
        if valid_groups:
            with ThreadPoolExecutor(max_workers=min(len(valid_groups), 32)) as executor:
                futures = [
                    executor.submit(_write_group_parallel, group_data, output_path, file_prefix, start_sep_end)
                    for group_data in valid_groups
                ]

                # 收集结果
                for future in as_completed(futures):
                    result = future.result()
                    if result > 0:
                        total_rows += result

        return total_rows
    except Exception as e:
        print(f"❌ 批处理错误: {e}")
        return 0


def split_df_by_days(
    input: pl.DataFrame,
    output_dir: str,
    time_col: str = 'trade_time',
    file_prefix: str = 'data',
    start_sep_end: str = START_SEP_END,
    n_jobs: int = -1,
    days_per_batch: int = 100
) -> None:
    """
    将DataFrame按日期切分成按天保存的文件 - 内存高效并行版本

    功能：
    将df（pl），按time_col列进行切分成按天保存的文件。
    文件名：{前缀}{START_SEP_END}年月日.parquet
    例如data__20240101.parquet

    Parameters:
    -----------
    input : pl.DataFrame
        输入的polars DataFrame
    output_dir : str
        输出目录路径
    time_col : str
        时间列名，默认 'trade_time'
    file_prefix : str
        文件名前缀，默认 'data'
    start_sep_end : str
        分隔符，默认使用常量 START_SEP_END
    n_jobs : int
        并行线程数，-1表示使用所有CPU核心
    days_per_batch : int
        每批处理的天数，默认100天，控制内存使用
    """
    if time_col not in input.columns:
        raise ValueError(f"时间列 '{time_col}' 在DataFrame中未找到")

    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    if n_jobs == -1:
        n_jobs = mp.cpu_count()  # 使用所有CPU核心

    # 统一配置polars性能参数
    _configure_polars_performance(n_jobs)

    # 第一步：使用parse_date_input_df进行日期转换，创建临时日期键列
    df_with_date = parse_date_input_df(input, time_col, output_col='__date_key', output_type='date')

    # 第二步：创建LazyFrame并延迟排序，避免内存翻倍
    # LazyFrame中的排序不会立即执行，而是在需要时由查询优化器处理
    df_lazy = df_with_date.lazy().sort('__date_key')

    # 第四步：获取所有唯一日期，用于分批处理（使用临时日期键列）
    unique_dates = (
        df_lazy
        .select(pl.col('__date_key'))
        .unique()
        .collect()
        .get_column('__date_key')
        .to_list()
    )

    unique_dates = sorted(unique_dates)
    total_dates = len(unique_dates)

    if total_dates == 0:
        print("⚠️ 警告：没有获取到任何唯一日期！")
        return

    print(f"📊 发现 {total_dates} 个唯一日期，日期范围: {min(unique_dates)} 到 {max(unique_dates)}")

    # 计算批次数量
    total_batches = (total_dates + days_per_batch - 1) // days_per_batch
    print(f"🔧 分批配置：每批处理 {days_per_batch} 天，共需 {total_batches} 批")

    # 第五步：高性能分批处理LazyFrame

    bar_format = "{desc}: {percentage:5.2f}%|{bar}{r_bar}"
    with tqdm(total=total_batches, desc="分批处理", initial=0, bar_format=bar_format, ncols=100) as batch_pbar:

        for batch_idx in range(0, total_dates, days_per_batch):
            batch_dates = unique_dates[batch_idx:batch_idx + days_per_batch]
            batch_num = batch_idx // days_per_batch + 1

            _process_date_range_batch(
                df_lazy,
                batch_dates,
                output_path,
                file_prefix,
                start_sep_end,
                '__date_key'  # 使用临时日期键列进行分组
            )

            batch_pbar.update(1)
            batch_pbar.set_postfix({
                "批次": f"{batch_num}/{total_batches}"
            })

    print(f"✅ 处理完成！共输出 {total_dates} 个文件")

def _merge_pd_dfs(*dfs, on, how):
    """
    合并多个DataFrame的函数。

    参数:
    on (list): 用于合并的列名列表。
    how (str): 合并的方式（'left', 'right', 'outer', 'inner'）。
    *dfs: dfs = [df1, df2, df3]

    返回:
    DataFrame: 合并后的DataFrame。

    举例：
    df = yqt.merge_dfs(df1, df2, df3, on=['date', 'symbol'], how='left')
    """
    return reduce(lambda left, right: pd.merge(left, right, on=on, how=how), dfs)

def merge_dfs(*dfs: List[Union[pd.DataFrame, pl.DataFrame]],
             on: List[str],
             how: Literal['left', 'right', 'full', 'inner','outer'],
             select: Optional[List[str]] = None,
             descending: Union[bool, List[bool]] = False,
             return_type:Literal['pd','pl']='pl') -> Union[pd.DataFrame, pl.DataFrame]:
    """
    合并多个DataFrame的函数。

    参数:
    on (list): 用于合并的列名列表。
    how (str): 合并的方式:
        - 'left': 以第一个DataFrame为基准进行对齐
        - 'inner': 只保留所有DataFrame中都存在的columns
        - 'full': 保留所有DataFrame中出现的columns
        - 'right': 以最后一个DataFrame基准进行对齐
    select (list, optional): 指定要保留的列及其顺序。
    descending (Union[bool, List[bool]], optional): 控制排序方向。可以是单个bool值(应用于所有列)
        或bool列表(与on中的列一一对应)。True表示降序,False表示升序。默认False。
    return_type (str): 返回数据类型，支持 'pd'(pandas DataFrame) 或 'pl'(polars DataFrame)。默认'pl'。
    *dfs: dfs = [df1, df2, df3]

    返回:
    DataFrame: 根据return_type参数返回pandas DataFrame或polars DataFrame。
    """
    # 参数验证
    if return_type not in ['pd', 'pl']:
        raise ValueError(f"不支持的返回类型: {return_type}。支持的类型: 'pd', 'pl'")

    if return_type == 'pd':
        return _merge_pd_dfs(*dfs, on=on, how=how)

    # 转换所有 DataFrame 为 polars 格式以确保类型兼容性
    polars_dfs = []
    for df in dfs:
        if isinstance(df, pd.DataFrame):
            polars_dfs.append(pl.from_pandas(df))
        else:
            polars_dfs.append(df)

    # 先对齐 - 现在类型安全
    aligned_dfs = pl.align_frames(*polars_dfs, on=on, how=how, descending=descending)
    
    # 合并对齐后的DataFrame
    result = aligned_dfs[0]
    for df in aligned_dfs[1:]:
        result = result.hstack(df.drop(on))
        
    # 如果指定了select,在合并后选择列
    if select is not None:
        result = result.select(select)
        
    return result

if __name__ == '__main__':
    # 测试函数
    test_input_path = 'D:/115_downloads/stock_1m'
    test_output = 'D:/115_downloads/output_test'

    # 读取测试数据
    test_df = pl.read_parquet(test_input_path)

    # 测试函数
    split_df_by_days(
        input=test_df,
        output_dir=test_output,
        time_col='trade_time',
        file_prefix='test'
    )

    print("\n" + "=" * 60)
    print("✅ 测试完成!")
    print("📁 输出目录:", test_output)