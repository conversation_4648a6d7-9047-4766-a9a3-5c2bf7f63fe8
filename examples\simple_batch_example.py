#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ddump批量下载框架简单使用示例

展示如何使用简化的BatchDump类进行批量下载
"""

import pandas as pd
import akshare as ak
from pathlib import Path
from loguru import logger

from ddump.api.batch_dump import BatchDump, MultiProcessDump


def simple_batch_example():
    """
    简单的批量下载示例
    """
    logger.info("🚀 ddump批量下载框架简单示例")
    
    # 准备测试数据
    stock_list = ['000001', '000002', '600000', '600036', '000858']
    temp_path = Path("./temp_example")
    temp_path.mkdir(exist_ok=True)
    
    # 创建批量下载实例（启用进度条）
    batch_dump = BatchDump(
        api_module='akshare',
        temp_path=str(temp_path),
        show_progress=True  # 🎯 启用进度条
    )
    
    # 设置下载参数
    download_params = {
        'period': 'daily',
        'start_date': '20250101',
        'end_date': '20250715',
        'adjust': ''
    }
    
    kw = ['symbol', 'period', 'start_date', 'end_date', 'adjust']
    
    try:
        # 开始下载（会显示进度条）
        logger.info("开始下载，观察进度条效果...")
        results = batch_dump.download(
            stock_list=stock_list,
            func_name='stock_zh_a_hist',
            download_params=download_params,
            kw=kw,
            retry_times=2,
            retry_delay=0.5
        )
        
        logger.info(f"✅ 下载完成，获得 {len(results)} 个有效结果")
        
        # 获取合并数据
        if results:
            merged_data = batch_dump.get_merged_data()
            logger.info(f"📊 合并数据量: {len(merged_data)} 条记录")
            print(merged_data.head())
        
    except Exception as e:
        logger.error(f"❌ 下载失败: {e}")
    
    finally:
        # 清理
        import shutil
        if temp_path.exists():
            shutil.rmtree(temp_path)


def compatibility_example():
    """
    向后兼容性示例（使用原MultiProcessDump接口）
    """
    logger.info("🔄 向后兼容性示例")
    
    # 使用原来的MultiProcessDump接口（实际上是BatchDump的别名）
    multi_dump = MultiProcessDump(
        api_module='akshare',
        temp_path='./temp_compat',
        max_workers=4,  # 这个参数会被忽略并提示
        enable_proxy=True,  # 这个参数会被忽略并提示
        show_progress=True
    )
    
    logger.info("✅ 向后兼容性验证通过")
    logger.info("💡 注意：max_workers和enable_proxy参数已被忽略")


def disable_progress_example():
    """
    禁用进度条的示例
    """
    logger.info("📝 禁用进度条示例")
    
    # 创建批量下载实例（禁用进度条）
    batch_dump = BatchDump(
        api_module='akshare',
        temp_path='./temp_no_progress',
        show_progress=False  # 🚫 禁用进度条
    )
    
    logger.info("进度条已禁用，将使用传统日志输出")


if __name__ == '__main__':
    logger.info("=" * 60)
    logger.info("🎯 ddump批量下载框架功能演示（简化版）")
    logger.info("=" * 60)
    
    # 示例1：简单批量下载
    logger.info("\n📍 示例1: 简单批量下载（带进度条）")
    simple_batch_example()
    
    # 示例2：向后兼容性
    logger.info("\n📍 示例2: 向后兼容性")
    compatibility_example()
    
    # 示例3：禁用进度条
    logger.info("\n📍 示例3: 禁用进度条")
    disable_progress_example()
    
    logger.info("\n🎉 演示完成！")
    logger.info("\n💡 使用提示:")
    logger.info("   1. 安装tqdm: pip install tqdm")
    logger.info("   2. 使用BatchDump类进行批量下载")
    logger.info("   3. 设置show_progress=True启用进度条")
    logger.info("   4. 设置show_progress=False禁用进度条")
    logger.info("   5. 如果未安装tqdm，进度条会自动禁用")
    logger.info("   6. 支持向后兼容，可继续使用MultiProcessDump")
    logger.info("\n🚀 优势:")
    logger.info("   ✅ 代码简洁，易于理解和维护")
    logger.info("   ✅ 进度条实时显示下载状态")
    logger.info("   ✅ 自动重试机制，提高成功率")
    logger.info("   ✅ 向后兼容，无需修改现有代码")
    logger.info("   ✅ 单进程实现，避免多进程复杂性")
