import polars as pl
import numpy as np
import pandas as pd
import pytz
from pathlib import Path
from datetime import datetime, date, timedelta
from typing import Literal, Union, Optional, List
from ddump.common import DATE, PUB_DATE, ROOT_DATA_DIR

__all__ = [
    'datetime_to_output',
    'format_list_dates',
    'get_nearest_trade_date',
    'get_nearest_trade_dates_df',
    'get_trade_date_now',
    'get_trade_dates',
    'parse_date_input_df'
]

def parse_date_input(date_input) -> Optional[datetime]:
    """
    将各种格式的日期输入转换为 datetime 对象
    支持: '20090101', 20090101, '2009-01-01', '2009-01-01 09:30:00', datetime对象等
    """
    if date_input is None:
        return None
    
    if isinstance(date_input, datetime):
        return date_input
    elif isinstance(date_input, date):
        return datetime.combine(date_input, datetime.min.time())
    
    # 转换为字符串处理
    date_str = str(date_input)
    
    # 移除常见分隔符，只保留数字
    clean_str = date_str.replace('-', '').replace('/', '').replace(' ', '').replace(':', '')
    
    # 提取日期部分 (前8位)
    if len(clean_str) >= 8:
        date_part = clean_str[:8]
        year = int(date_part[:4])
        month = int(date_part[4:6])
        day = int(date_part[6:8])
        
        # 如果有时间部分，也提取
        hour = minute = second = 0
        if len(clean_str) >= 10:
            hour = int(clean_str[8:10])
        if len(clean_str) >= 12:
            minute = int(clean_str[10:12])
        if len(clean_str) >= 14:
            second = int(clean_str[12:14])

        return datetime(year, month, day, hour, minute, second)
    
    raise ValueError(f"无法解析日期格式: {date_input}")


def _load_trade_calendar() -> List[datetime]:
    """
    从 calendars.txt 文件读取交易日历数据并转换为 datetime 对象列表

    优先从 ROOT_DATA_DIR 读取，如果不存在则从当前文件所在目录读取

    Returns:
        List[datetime]: 交易日期列表，按时间顺序排列

    Raises:
        FileNotFoundError: 当 calendars.txt 文件不存在时
        ValueError: 当文件格式错误时
    """
    try:
        # 优先从 ROOT_DATA_DIR 读取
        calendar_file = Path(ROOT_DATA_DIR) / 'calendars.txt'

        # 如果 ROOT_DATA_DIR 中不存在，尝试从当前文件所在目录读取
        if not calendar_file.exists():
            calendar_file = Path(__file__).parent / 'calendars.txt'

        if not calendar_file.exists():
            calendars_py = Path(__file__).parent / 'calendars.py'
            raise FileNotFoundError(
                f"交易日历文件不存在。已尝试以下位置:\n"
                f"1. {Path(ROOT_DATA_DIR) / 'calendars.txt'}\n"
                f"2. {Path(__file__).parent / 'calendars.txt'}\n\n"
                f"请运行以下命令生成交易日历文件:\n"
                f"python {calendars_py}"
            )

        with open(calendar_file, 'r', encoding='utf-8') as f:
            date_ints = [int(line.strip()) for line in f if line.strip()]

        if not date_ints:
            raise ValueError("交易日历文件为空或格式错误")

        # 转换为 datetime 对象
        trade_dates = []
        for date_int in date_ints:
            try:
                date_str = str(date_int)
                if len(date_str) != 8:
                    raise ValueError(f"日期格式错误: {date_int}，应为 YYYYMMDD 格式")

                year = int(date_str[:4])
                month = int(date_str[4:6])
                day = int(date_str[6:8])
                trade_dates.append(datetime(year, month, day))
            except (ValueError, IndexError) as e:
                raise ValueError(f"解析日期失败: {date_int}，错误: {e}")

        return trade_dates

    except FileNotFoundError:
        raise
    except Exception as e:
        raise ValueError(f"读取交易日历失败: {e}")


def parse_date_input_df(df: Union[pl.DataFrame, pl.LazyFrame, pd.DataFrame],
                       date_col: str,
                       output_col: Optional[str] = None,
                       output_type: Literal['int','float','str','_str_','date','datetime'] = 'datetime') -> Union[pl.DataFrame, pl.LazyFrame, pd.DataFrame]:
    """
    解析DataFrame中的日期列，支持各种日期格式的向量化转换
    兼容pandas DataFrame、polars DataFrame和polars LazyFrame

    支持的输入格式:
    - 整数: 20090101, 20090101103159
    - 字符串: '20090101', '2009-01-01', '2009-01-01 10:31:59'
    - datetime对象
    - timestamp (ms/ns)

    Args:
        df: 输入DataFrame (pandas DataFrame、polars DataFrame或polars LazyFrame)
        date_col: 要解析的日期列名
        output_col: 输出列名，如果为None则覆盖原列
        output_type: 输出格式类型 ('int','float','str','_str_','date','datetime')

    Returns:
        解析后的DataFrame (与输入类型相同)
        - pandas DataFrame → pandas DataFrame
        - polars DataFrame → polars DataFrame
        - polars LazyFrame → polars LazyFrame
    """
    if output_col is None:
        output_col = date_col

    # 类型检测逻辑
    is_pandas = isinstance(df, pd.DataFrame)
    is_lazy = isinstance(df, pl.LazyFrame)

    if is_pandas:
        # ===== PANDAS 内存安全处理 =====
        date_series = df[date_col]

        # 简化处理：直接转换为datetime
        if pd.api.types.is_datetime64_any_dtype(date_series):
            parsed_series = date_series
        else:
            parsed_series = pd.to_datetime(date_series, errors='coerce')

        # 转换输出格式并原地修改
        if output_type == 'datetime':
            if output_col == date_col:
                # 原地修改
                df[output_col] = parsed_series
                return df
            else:
                # 添加新列，避免复制整个DataFrame
                return df.assign(**{output_col: parsed_series})
        else:
            # 🚀 超高性能pandas向量化转换 - 避免重复计算
            if output_type == 'datetime':
                output_series = parsed_series
            elif output_type == 'date':
                output_series = parsed_series.dt.date
            elif output_type in ['int', 'float', 'str', '_str_']:
                # 🚀 超级优化：直接使用最快的pandas操作
                if output_type == 'int':
                    # 最快路径：直接strftime转换，不做复杂判断
                    output_series = parsed_series.dt.strftime('%Y%m%d%H%M%S').astype('Int64')
                elif output_type == 'float':
                    # 先转为int64再转float，避免科学计数法
                    output_series = parsed_series.dt.strftime('%Y%m%d%H%M%S').astype('Int64').astype('float64')
                    # 设置显示格式，避免科学计数法显示
                    pd.set_option('display.float_format', '{:.1f}'.format)
                elif output_type == 'str':
                    output_series = parsed_series.dt.strftime('%Y%m%d%H%M%S')
                else:  # '_str_'
                    output_series = parsed_series.dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                raise ValueError(f"不支持的输出类型: {output_type}")
            if output_col == date_col:
                # 原地修改
                df[output_col] = output_series
                return df
            else:
                # 添加新列
                return df.assign(**{output_col: output_series})

    else:
        # ===== POLARS 超高性能处理 =====
        # 针对真实数据的最优化方案

        # 适配 LazyFrame 的列类型获取
        if is_lazy:
            schema = df.collect_schema()
            col_dtype = schema[date_col]
        else:
            date_series = df[date_col]
            col_dtype = date_series.dtype

        # 🚀 最关键优化：针对datetime列的直接转换
        if col_dtype in [pl.Datetime, pl.Date]:
            if output_type == 'int':
                # 根据数据类型选择合适的格式
                if col_dtype == pl.Date:
                    # Date类型只有日期，使用YYYYMMDD格式
                    format_str = '%Y%m%d'
                else:
                    # Datetime类型包含时间，使用完整格式
                    format_str = '%Y%m%d%H%M%S'
                return df.with_columns(
                    pl.col(date_col).dt.strftime(format_str).cast(pl.Int64).alias(output_col)
                )
            elif output_type == 'float':
                # 设置polars显示格式，避免科学计数法
                pl.Config.set_float_precision(1)
                pl.Config.set_fmt_float("full")
                # 根据数据类型选择合适的格式
                if col_dtype == pl.Date:
                    format_str = '%Y%m%d'
                else:
                    format_str = '%Y%m%d%H%M%S'
                return df.with_columns(
                    pl.col(date_col).dt.strftime(format_str).cast(pl.Float64).alias(output_col)
                )
            elif output_type == 'str':
                # 根据数据类型选择合适的格式
                if col_dtype == pl.Date:
                    format_str = '%Y%m%d'
                else:
                    format_str = '%Y%m%d%H%M%S'
                return df.with_columns(
                    pl.col(date_col).dt.strftime(format_str).alias(output_col)
                )
            elif output_type == '_str_':
                return df.with_columns(
                    pl.col(date_col).dt.strftime('%Y-%m-%d %H:%M:%S').alias(output_col)
                )
            elif output_type == 'date':
                return df.with_columns(
                    pl.col(date_col).dt.date().alias(output_col)
                )
            else:  # datetime
                return df.with_columns(
                    pl.col(date_col).alias(output_col)
                )

        # 对于int/float输出类型，使用超高性能方案
        if output_type in ['int', 'float'] and col_dtype in [pl.Int64, pl.Int32, pl.Float64, pl.Float32]:
            # 超高性能：直接类型转换，避免复杂的日期解析
            if output_type == 'int':
                output_series = pl.col(date_col).cast(pl.Int64)
            else:
                # 设置polars显示格式
                pl.Config.set_float_precision(1)
                pl.Config.set_fmt_float("full")
                output_series = pl.col(date_col).cast(pl.Float64)
            return df.with_columns(output_series.alias(output_col))

        # 对于字符串输入，先清理再转换
        if col_dtype == pl.Utf8:
            if output_type in ['int', 'float']:
                # 清理字符串并转换为数值
                clean_series = pl.col(date_col).str.replace_all(r"[-/:\s]", "")
                if output_type == 'int':
                    output_series = clean_series.cast(pl.Int64, strict=False)
                else:
                    # 设置polars显示格式
                    pl.Config.set_float_precision(1)
                    pl.Config.set_fmt_float("full")
                    output_series = clean_series.cast(pl.Float64, strict=False)
                return df.with_columns(output_series.alias(output_col))

        # 回退到完整的日期解析（仅在必要时使用）
        # 这部分保持原有逻辑，但只在简化方案无法处理时才使用
        try:
            # 先提取并解析日期列（适配 LazyFrame）
            if not is_lazy:
                date_series = df[date_col]

            # 分步处理（使用表达式，兼容 LazyFrame）
            if col_dtype in [pl.Datetime, pl.Date]:
                if is_lazy:
                    parsed_expr = pl.col(date_col).cast(pl.Datetime)
                else:
                    parsed_series = date_series.cast(pl.Datetime)
            elif col_dtype in [pl.Int64, pl.Int32, pl.Float64, pl.Float32]:
                if is_lazy:
                    # LazyFrame 使用表达式
                    parsed_expr = (
                        pl.col(date_col).cast(pl.Utf8)
                        .str.to_datetime(format="%Y%m%d", strict=False)
                        .fill_null(
                            pl.col(date_col).cast(pl.Utf8)
                            .str.to_datetime(format="%Y%m%d%H%M%S", strict=False)
                        )
                    )
                else:
                    # DataFrame 保持原有逻辑
                    str_series = date_series.cast(pl.Utf8)
                    try:
                        parsed_series = str_series.str.to_datetime(format="%Y%m%d", strict=False)
                        null_mask = parsed_series.is_null()
                        if null_mask.any():
                            parsed_series = parsed_series.fill_null(
                                str_series.str.to_datetime(format="%Y%m%d%H%M%S", strict=False)
                            )
                    except:
                        parsed_series = pl.Series([None] * len(date_series), dtype=pl.Datetime)
            else:
                # 字符串类型
                if is_lazy:
                    # LazyFrame 使用表达式
                    parsed_expr = (
                        pl.col(date_col).str.to_datetime(strict=False)
                        .fill_null(
                            pl.col(date_col).str.replace_all(r"[-/:\s]", "")
                            .str.to_datetime(format="%Y%m%d", strict=False)
                        )
                        .fill_null(
                            pl.col(date_col).str.replace_all(r"[-/:\s]", "")
                            .str.to_datetime(format="%Y%m%d%H%M%S", strict=False)
                        )
                    )
                else:
                    # DataFrame 保持原有逻辑
                    try:
                        parsed_series = date_series.str.to_datetime(strict=False)
                        null_mask = parsed_series.is_null() & date_series.is_not_null()
                        if null_mask.any():
                            clean_series = date_series.str.replace_all(r"[-/:\s]", "")
                            try:
                                temp_parsed = clean_series.str.to_datetime(format="%Y%m%d", strict=False)
                                parsed_series = parsed_series.fill_null(temp_parsed)
                            except:
                                pass
                            null_mask = parsed_series.is_null() & date_series.is_not_null()
                            if null_mask.any():
                                try:
                                    temp_parsed = clean_series.str.to_datetime(format="%Y%m%d%H%M%S", strict=False)
                                    parsed_series = parsed_series.fill_null(temp_parsed)
                                except:
                                    pass
                    except:
                        parsed_series = pl.Series([None] * len(date_series), dtype=pl.Datetime)

            # 转换输出格式（适配 LazyFrame）
            if is_lazy:
                # LazyFrame 使用表达式
                if output_type == 'datetime':
                    output_expr = parsed_expr
                else:
                    has_time_mask = (
                        (parsed_expr.dt.hour() != 0) |
                        (parsed_expr.dt.minute() != 0) |
                        (parsed_expr.dt.second() != 0)
                    )

                    if output_type == 'int':
                        date_only = parsed_expr.dt.strftime("%Y%m%d").cast(pl.Int64)
                        date_time = parsed_expr.dt.strftime("%Y%m%d%H%M%S").cast(pl.Int64)
                        output_expr = pl.when(has_time_mask).then(date_time).otherwise(date_only)
                    elif output_type == 'float':
                        pl.Config.set_float_precision(1)
                        pl.Config.set_fmt_float("full")
                        date_only = parsed_expr.dt.strftime("%Y%m%d").cast(pl.Float64)
                        date_time = parsed_expr.dt.strftime("%Y%m%d%H%M%S").cast(pl.Float64)
                        output_expr = pl.when(has_time_mask).then(date_time).otherwise(date_only)
                    elif output_type == 'str':
                        date_only = parsed_expr.dt.strftime("%Y%m%d")
                        date_time = parsed_expr.dt.strftime("%Y%m%d%H%M%S")
                        output_expr = pl.when(has_time_mask).then(date_time).otherwise(date_only)
                    elif output_type == '_str_':
                        date_only = parsed_expr.dt.strftime("%Y-%m-%d")
                        date_time = parsed_expr.dt.strftime("%Y-%m-%d %H:%M:%S")
                        output_expr = pl.when(has_time_mask).then(date_time).otherwise(date_only)
                    elif output_type == 'date':
                        output_expr = parsed_expr.dt.date()
                    else:
                        raise ValueError(f"不支持的输出类型: {output_type}")

                return df.with_columns(output_expr.alias(output_col))
            else:
                # DataFrame 保持原有逻辑
                if output_type == 'datetime':
                    output_series = parsed_series
                else:
                    has_time_mask = (
                        (parsed_series.dt.hour() != 0) |
                        (parsed_series.dt.minute() != 0) |
                        (parsed_series.dt.second() != 0)
                    )

                    if output_type == 'int':
                        date_only = parsed_series.dt.strftime("%Y%m%d").cast(pl.Int64)
                        date_time = parsed_series.dt.strftime("%Y%m%d%H%M%S").cast(pl.Int64)
                        output_series = pl.when(has_time_mask).then(date_time).otherwise(date_only)
                    elif output_type == 'float':
                        pl.Config.set_float_precision(1)
                        pl.Config.set_fmt_float("full")
                        date_only = parsed_series.dt.strftime("%Y%m%d").cast(pl.Float64)
                        date_time = parsed_series.dt.strftime("%Y%m%d%H%M%S").cast(pl.Float64)
                        output_series = pl.when(has_time_mask).then(date_time).otherwise(date_only)
                    elif output_type == 'str':
                        date_only = parsed_series.dt.strftime("%Y%m%d")
                        date_time = parsed_series.dt.strftime("%Y%m%d%H%M%S")
                        output_series = pl.when(has_time_mask).then(date_time).otherwise(date_only)
                    elif output_type == '_str_':
                        date_only = parsed_series.dt.strftime("%Y-%m-%d")
                        date_time = parsed_series.dt.strftime("%Y-%m-%d %H:%M:%S")
                        output_series = pl.when(has_time_mask).then(date_time).otherwise(date_only)
                    elif output_type == 'date':
                        output_series = parsed_series.dt.date()
                    else:
                        raise ValueError(f"不支持的输出类型: {output_type}")

                # 使用with_columns返回结果
                if isinstance(output_series, pl.Expr):
                    return df.with_columns(output_series.alias(output_col))
                else:
                    return df.with_columns(pl.Series(output_col, output_series))

        except Exception as e:
            # 如果所有方法都失败，返回原始数据
            print(f"警告：日期解析失败，返回原始数据。错误：{e}")
            return df.with_columns(pl.col(date_col).alias(output_col))


def datetime_to_output(dt: datetime, output_type: str):
    """将 datetime 转换为指定的输出格式，智能处理日期和时间"""
    if dt is None:
        return None

    # 检查是否包含时间信息（非零时分秒）
    has_time = dt.hour != 0 or dt.minute != 0 or dt.second != 0

    if output_type == 'int':
        if has_time:
            return int(dt.strftime('%Y%m%d%H%M%S'))  # YYYYMMDDHHMMSS
        else:
            return int(dt.strftime('%Y%m%d'))        # YYYYMMDD
    elif output_type == 'np_int':
        if has_time:
            return np.int64(dt.strftime('%Y%m%d%H%M%S'))
        else:
            return np.int64(dt.strftime('%Y%m%d'))
    elif output_type == 'float':
        if has_time:
            return float(dt.strftime('%Y%m%d%H%M%S'))
        else:
            return float(dt.strftime('%Y%m%d'))
    elif output_type == 'str':
        if has_time:
            return dt.strftime('%Y%m%d%H%M%S')       # YYYYMMDDHHMMSS
        else:
            return dt.strftime('%Y%m%d')             # YYYYMMDD
    elif output_type == '_str_':
        if has_time:
            return dt.strftime('%Y-%m-%d %H:%M:%S')  # YYYY-MM-DD HH:MM:SS
        else:
            return dt.strftime('%Y-%m-%d')           # YYYY-MM-DD
    elif output_type == 'date':
        return dt.date()
    elif output_type == 'datetime':
        return dt
    else:
        raise ValueError(f"不支持的输出类型: {output_type}。支持的类型: int, np_int, float, str, _str_, date, datetime")


def format_list_dates(dates_list: Union[list, np.ndarray],
                     output_date_type: Literal['int','np_int','float','str','_str_','date','datetime'] = 'int',
                     freq: Literal['D','MS','ME','Q','Y'] = 'D'):
    """
    将数组或列表中的所有日期转换为指定格式，支持不同频率

    :param data: 包含日期的数组或列表
    :param output_date_type: 输出日期格式
    :param freq: 输出的日期粒度，'D'=日度，'MS'=月初，'ME'=月末，'Q'=季度，'Y'=年度
    :return: 格式化后的日期列表
    """
    # 转换所有日期为 datetime 对象
    datetime_list = []
    for item in dates_list:
        dt = parse_date_input(item)
        if dt:
            datetime_list.append(dt)

    # 根据频率进行转换
    if freq == 'D':
        # 日度数据，直接使用
        result_dates = datetime_list
    elif freq in ['MS', 'ME']:
        # 按月份分组
        month_groups = {}
        for dt in datetime_list:
            month_key = dt.strftime('%Y%m')
            if month_key not in month_groups:
                month_groups[month_key] = []
            month_groups[month_key].append(dt)

        # 对每个月份取第一个（月初）或最后一个（月末）交易日
        if freq == 'ME':  # 月末处理
            result_dates = [max(group_dates) for group_dates in month_groups.values()]
        else:  # 月初处理
            result_dates = [min(group_dates) for group_dates in month_groups.values()]
    elif freq == 'Q':
        # 季度末处理
        quarter_groups = {}
        for dt in datetime_list:
            quarter = (dt.month - 1) // 3 + 1
            quarter_key = f"{dt.year}Q{quarter}"
            if quarter_key not in quarter_groups:
                quarter_groups[quarter_key] = []
            quarter_groups[quarter_key].append(dt)

        result_dates = [max(group_dates) for group_dates in quarter_groups.values()]
    elif freq == 'Y':
        # 年度处理
        year_groups = {}
        for dt in datetime_list:
            year_key = str(dt.year)
            if year_key not in year_groups:
                year_groups[year_key] = []
            year_groups[year_key].append(dt)

        result_dates = [max(group_dates) for group_dates in year_groups.values()]
    else:
        raise ValueError(f"不支持的频率: {freq}")

    # 去重并排序
    result_dates = sorted(set(result_dates))

    # 转换为指定输出格式
    return [datetime_to_output(dt, output_date_type) for dt in result_dates]


def get_nearest_trade_date(x,
                          trade_dates: Union[list, np.ndarray],
                          direction: Literal['backward','forward'] = 'backward',
                          output_date_type: Literal['int','np_int','float','str','_str_','date','datetime'] = 'int'):
    """
    将单个日期x对齐到最近的交易日，根据指定的方向进行对齐

    :param x: 输入日期
    :param trade_dates: 交易日列表
    :param direction: 对齐方向 ('backward', 'forward')
    :param output_date_type: 返回日期的格式类型
    :return: 对齐后的交易日，如果找不到合适的日期，返回 None
    """
    # 转换输入日期
    target_dt = parse_date_input(x)
    if target_dt is None:
        return None

    # 转换交易日列表为 datetime 对象
    trade_dt_list = []
    for item in trade_dates:
        dt = parse_date_input(item)
        if dt:
            trade_dt_list.append(dt)

    trade_dt_list = sorted(trade_dt_list)

    result_dt = None

    if direction == 'backward':
        # 找到第一个不大于 target_dt 的日期
        for dt in reversed(trade_dt_list):
            if dt <= target_dt:
                result_dt = dt
                break
    elif direction == 'forward':
        # 找到第一个大于或等于 target_dt 的日期
        for dt in trade_dt_list:
            if dt >= target_dt:
                result_dt = dt
                break
    else:
        raise ValueError("Invalid direction. Choose from 'backward', 'forward'.")

    if result_dt is not None:
        return datetime_to_output(result_dt, output_date_type)

    return None


def get_nearest_trade_dates_df(df: Union[pl.DataFrame, pd.DataFrame],
                             trade_dates: Union[list, np.ndarray],
                             pub_date_col: str = PUB_DATE,
                             date_col: str = DATE,
                             direction: Literal['backward','forward'] = 'backward',
                             output_date_type: Literal['int','np_int','float','str','_str_','date','datetime'] = 'int'):
    """
    将日期对齐到最近的交易日，支持 pandas 和 polars DataFrame
    ⚠️  内存安全：避免大表格式转换，使用原生处理逻辑

    参数：
    df: 包含日期的 DataFrame (pandas 或 polars)
    trade_dates: 交易日列表或数组
    pub_date_col: 公告日期列名
    date_col: 对齐后的日期列名
    direction: 'backward'=previous(周日的公告对齐到周五); 'forward'=next(周日的公告对齐到周一)
    output_date_type: 输出日期格式

    返回：
    对齐后的 DataFrame (与输入类型相同)
    """
    # 检测输入类型
    is_pandas = isinstance(df, pd.DataFrame)

    # 预处理交易日期
    trade_dt_list = []
    for item in trade_dates:
        dt = parse_date_input(item)
        if dt:
            trade_dt_list.append(dt)
    trade_dates_int = [int(dt.strftime('%Y%m%d')) for dt in sorted(trade_dt_list)]

    if is_pandas:
        # ===== PANDAS 原生处理 (内存安全) =====
        df_copy = df.copy()

        # 转换公告日期列为整数
        pub_dates_int = []
        for pub_date_value in df_copy[pub_date_col]:
            dt = parse_date_input(pub_date_value)
            pub_dates_int.append(int(dt.strftime('%Y%m%d')) if dt else None)

        df_copy[pub_date_col + '_int'] = pub_dates_int

        # 使用 pandas merge_asof 进行日期对齐
        trade_dates_df = pd.DataFrame({date_col: trade_dates_int}).sort_values(date_col)

        result_df = pd.merge_asof(
            df_copy.sort_values(pub_date_col + '_int'),
            trade_dates_df,
            left_on=pub_date_col + '_int',
            right_on=date_col,
            direction=direction
        )

        # 恢复原始公告日期列
        result_df[pub_date_col] = result_df[pub_date_col + '_int']
        result_df = result_df.drop(columns=[pub_date_col + '_int'])

        # 转换输出格式
        if output_date_type != 'int':
            formatted_dates = []
            for date_int in result_df[date_col]:
                if pd.notna(date_int):
                    dt = parse_date_input(int(date_int))
                    formatted_dates.append(datetime_to_output(dt, output_date_type) if dt else None)
                else:
                    formatted_dates.append(None)
            result_df[date_col] = formatted_dates

        return result_df

    else:
        # ===== POLARS 原生处理 =====
        pl_df = df.clone()
        trade_dates_df = pl.DataFrame({date_col: trade_dates_int}).sort(date_col)

        # 转换公告日期列为整数
        pub_dates_int = []
        for i in range(len(pl_df)):
            pub_date_value = pl_df[pub_date_col][i]
            dt = parse_date_input(pub_date_value)
            pub_dates_int.append(int(dt.strftime('%Y%m%d')) if dt else None)

        pl_df = pl_df.with_columns(pl.Series(pub_date_col, pub_dates_int)).sort(pub_date_col)

        # 使用 polars join_asof 对齐日期
        result_df = pl_df.join_asof(
            trade_dates_df,
            left_on=pub_date_col,
            right_on=date_col,
            strategy=direction,
            coalesce=False
        )

        # 转换输出日期格式
        if output_date_type != 'int':
            aligned_dates = []
            for date_int in result_df[date_col].to_list():
                if date_int is not None:
                    dt = parse_date_input(date_int)
                    aligned_dates.append(datetime_to_output(dt, output_date_type) if dt else None)
                else:
                    aligned_dates.append(None)
            result_df = result_df.with_columns(pl.Series(date_col, aligned_dates))

        # 重新排序列
        all_cols = list(df.columns)
        if date_col not in all_cols:
            date_idx = all_cols.index(pub_date_col) + 1
            all_cols.insert(date_idx, date_col)
        result_df = result_df.select(all_cols)

        return result_df


def get_trade_date_now(output_date_type: Literal['int','np_int','float','str','_str_','date','datetime'] = 'int'):
    """获取最近结束的交易日
    自动从交易日历文件读取数据，无需手动传入交易日列表
    为了保证数据17点30分前不入库,所以设置17点30分为结束交易时间
    使用上海时区(UTC+8),防止其他程序显示的日期不对,比如dagster任务

    Args:
        output_date_type: 输出日期格式类型，支持以下格式：
            - 'int': 整数格式 (20250627)
            - 'np_int': numpy整数格式
            - 'float': 浮点数格式 (20250627.0)
            - 'str': 字符串格式 ('20250627')
            - '_str_': 带分隔符字符串格式 ('2025-06-27')
            - 'date': Python date 对象
            - 'datetime': Python datetime 对象

    Returns:
        根据 output_date_type 返回相应格式的最近交易日

    Raises:
        FileNotFoundError: 当交易日历文件不存在时
        ValueError: 当交易日历文件格式错误或 output_date_type 不支持时

    Example:
        >>> get_trade_date_now()  # 默认返回整数格式
        20250627
        >>> get_trade_date_now('_str_')  # 返回带分隔符的字符串
        '2025-06-27'
        >>> get_trade_date_now('datetime')  # 返回 datetime 对象
        datetime.datetime(2025, 6, 27, 0, 0)
    """
    # 获取交易日历
    trade_dates = _load_trade_calendar()

    # 保持原有的时区处理逻辑
    shanghai_tz = pytz.timezone('Asia/Shanghai')
    current_time = datetime.now(shanghai_tz) - timedelta(hours=17, minutes=30)
    # 转换为naive datetime以便比较
    current_time_naive = current_time.replace(tzinfo=None)

    return get_nearest_trade_date(
        x=current_time_naive,
        trade_dates=trade_dates,
        direction='backward',
        output_date_type=output_date_type
    )


def get_trade_dates(
    start_date=None,
    end_date=None,
    output_date_type: Literal['int','np_int','float','str','_str_','date','datetime'] = 'int',
    freq: Literal['D', 'MS','ME', 'Q', 'Y'] = 'D',
    end_date_now: bool = False,
    return_type: Literal['list','pl','pd'] = 'list'
    ) -> Union[List, pl.DataFrame, pd.DataFrame]:
    """
    获取交易时间列表（支持日级和分钟级）

    Args:
        start_date: 开始日期，默认为20000101，如果早于20000101会自动修正为20000101
        end_date: 结束日期
        output_date_type: 输出日期类型
        freq: 频率，'D'=日度，'MS'=月初，'ME'=月末，'Q'=季度，'Y'=年度
        end_date_now: 是否使用当前交易日作为结束日期
        return_type: 返回类型，'list'=列表，'pl'=polars DataFrame，'pd'=pandas DataFrame
    """
    # 1. 参数验证
    if end_date_now and end_date is not None:
        raise ValueError('end_date_now=True 时不能指定 end_date')
    
    # 2. 获取交易日历
    trade_dates = _load_trade_calendar()
    
    # 3. 处理开始日期
    min_date = datetime(2000, 1, 1)  # 修改为2000年，支持更早的历史数据
    if start_date is None:
        start_dt = min_date
    else:
        start_dt = parse_date_input(start_date)
        if start_dt is None or start_dt < min_date:
            start_dt = min_date
    
    # 4. 处理结束日期
    if end_date_now:
        # 使用内联逻辑计算当前交易日，避免循环依赖
        shanghai_tz = pytz.timezone('Asia/Shanghai')
        current_time = datetime.now(shanghai_tz) - timedelta(hours=16, minutes=30)
        current_time_naive = current_time.replace(tzinfo=None)

        # 直接使用内联逻辑确保返回 datetime 对象
        result_dt = None
        for trade_dt in trade_dates:
            if trade_dt <= current_time_naive:
                result_dt = trade_dt
            else:
                break
        end_dt = result_dt
    elif end_date is not None:
        end_dt = parse_date_input(end_date)
    else:
        end_dt = None
    
    # 5. 过滤交易日期
    filtered_dates = []
    for trade_dt in trade_dates:
        if trade_dt >= start_dt:
            if end_dt is None or trade_dt <= end_dt:
                filtered_dates.append(trade_dt)

    # 6. 根据频率处理日期并转换输出格式
    output_dates = format_list_dates(filtered_dates, output_date_type, freq)
    
    # 7. 根据return_type返回相应格式
    if return_type == 'pl':
        df = pl.DataFrame({DATE: output_dates}).sort(DATE)
        return df
    elif return_type == 'pd':
        df = pd.DataFrame({DATE: output_dates}).sort_values(DATE)
        return df
    elif return_type == 'list':
        return output_dates
    else:
        raise ValueError(f"不支持的返回类型: {return_type}。支持的类型: list, pl, pd")



if __name__ == '__main__':
    # 测试代码
    dates = get_trade_dates(start_date='20060101',end_date_now=True,return_type='pl')
    print("测试结果:", dates)
